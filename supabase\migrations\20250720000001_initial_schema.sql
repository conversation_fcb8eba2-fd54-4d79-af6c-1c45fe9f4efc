-- SEO Analysis Tool - Initial Database Schema
-- This migration creates all the core tables needed for the SEO analysis application

-- Enable necessary extensions
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- =====================================================
-- USERS AND AUTHENTICATION TABLES
-- =====================================================

-- Users table for multi-user support
CREATE TABLE IF NOT EXISTS users (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    email VARCHAR(255) UNIQUE NOT NULL,
    username VARCHAR(100) UNIQUE NOT NULL,
    password_hash VARCHAR(255),
    first_name VARCHAR(100),
    last_name VA<PERSON><PERSON><PERSON>(100),
    is_active BOOLEAN DEFAULT true,
    is_verified BOOLEAN DEFAULT false,
    role VARCHAR(50) DEFAULT 'user',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    last_login TIMESTAMP WITH TIME ZONE,
    email_verified_at TIMESTAMP WITH TIME ZONE,
    password_reset_token VARCHAR(255),
    password_reset_expires TIMESTAMP WITH TIME ZONE,
    verification_token VARCHAR(255),
    verification_expires TIMESTAMP WITH TIME ZONE
);

-- User sessions for authentication
CREATE TABLE IF NOT EXISTS user_sessions (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID REFERENCES users(id) ON DELETE CASCADE,
    token_hash VARCHAR(255) UNIQUE NOT NULL,
    refresh_token_hash VARCHAR(255),
    expires_at TIMESTAMP WITH TIME ZONE NOT NULL,
    refresh_expires_at TIMESTAMP WITH TIME ZONE,
    ip_address INET,
    user_agent TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- =====================================================
-- CORE SEO ANALYSIS TABLES
-- =====================================================

-- Sites table - Central registry of websites being analyzed
CREATE TABLE IF NOT EXISTS sites (
    id BIGSERIAL PRIMARY KEY,
    domain TEXT NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    domain_property TEXT,
    ga_property_id TEXT,
    service_account_data JSONB,
    homepage TEXT,
    last_updated TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    wp_api_key TEXT,
    last_analysis_date TIMESTAMP WITH TIME ZONE,
    user_id UUID REFERENCES users(id) ON DELETE SET NULL,
    
    -- Cached statistics for fast dashboard loading
    stats_pages INTEGER DEFAULT 0,
    stats_keywords INTEGER DEFAULT 0,
    stats_internal_links INTEGER DEFAULT 0,
    stats_external_links INTEGER DEFAULT 0,
    stats_jump_links INTEGER DEFAULT 0,
    stats_traffic_records INTEGER DEFAULT 0,
    stats_analytics_records INTEGER DEFAULT 0,
    stats_last_updated TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Pages table - Website page data and SEO metadata
CREATE TABLE IF NOT EXISTS pages (
    id BIGSERIAL PRIMARY KEY,
    site_id BIGINT REFERENCES sites(id) ON DELETE CASCADE,
    "URL" TEXT NOT NULL,
    snapshot_date DATE NOT NULL,
    "SEO Title" TEXT,
    "Meta Description" TEXT,
    "H1" TEXT,
    "Page Content" TEXT,
    "Focus Keyword" TEXT,
    "Page Type" TEXT,
    "Topic" TEXT,
    "Title Length" INTEGER,
    "GSC Clicks" INTEGER,
    "GSC Impressions" INTEGER,
    "CTR" NUMERIC,
    "Position" NUMERIC,
    "Google Analytics Page Views" INTEGER,
    url_hash TEXT,
    content_hash TEXT,
    raw_html TEXT
);

-- GSC Keywords table - Google Search Console keyword performance
CREATE TABLE IF NOT EXISTS gsc_keywords (
    id BIGSERIAL PRIMARY KEY,
    site_id BIGINT REFERENCES sites(id) ON DELETE CASCADE,
    "URL" TEXT NOT NULL,
    "Keyword" TEXT NOT NULL,
    "Month" TEXT NOT NULL,
    "Clicks" INTEGER,
    "Impressions" INTEGER,
    "CTR" NUMERIC,
    "Position" NUMERIC,
    keyword_hash TEXT
);

-- GSC Traffic table - Aggregated GSC traffic by page and month
CREATE TABLE IF NOT EXISTS gsc_traffic (
    id BIGSERIAL PRIMARY KEY,
    site_id BIGINT REFERENCES sites(id) ON DELETE CASCADE,
    "URL" TEXT NOT NULL,
    "Month" TEXT NOT NULL,
    "Clicks" INTEGER,
    "Impressions" INTEGER,
    "CTR" NUMERIC,
    "Position" NUMERIC,
    traffic_hash TEXT
);

-- GA Data table - Google Analytics page performance
CREATE TABLE IF NOT EXISTS ga_data (
    id BIGSERIAL PRIMARY KEY,
    site_id BIGINT REFERENCES sites(id) ON DELETE CASCADE,
    "URL" TEXT NOT NULL,
    "Month" TEXT NOT NULL,
    "Google Analytics Page Views" INTEGER,
    "Active Users" INTEGER
);

-- Internal Links table - All link analysis (internal, external, and jump links)
CREATE TABLE IF NOT EXISTS internal_links (
    id BIGSERIAL PRIMARY KEY,
    site_id BIGINT REFERENCES sites(id) ON DELETE CASCADE,
    "URL" TEXT NOT NULL,
    "Target Hyperlink" TEXT NOT NULL,
    "Anchor Text" TEXT,
    link_hash TEXT NOT NULL,
    snapshot_date DATE NOT NULL,
    "Link Type" TEXT,
    "URL Topic" TEXT,
    "Target Title" TEXT,
    "Relevance Score" NUMERIC,
    "Link Count" INTEGER
);

-- =====================================================
-- TASK MANAGEMENT TABLES
-- =====================================================

-- Tasks table for background job management
CREATE TABLE IF NOT EXISTS tasks (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    site_id BIGINT REFERENCES sites(id) ON DELETE CASCADE,
    user_id UUID REFERENCES users(id) ON DELETE SET NULL,
    task_type TEXT NOT NULL,
    status TEXT NOT NULL DEFAULT 'pending',
    progress INTEGER DEFAULT 0,
    total_steps INTEGER DEFAULT 100,
    current_step TEXT,
    error_message TEXT,
    result_data JSONB,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    started_at TIMESTAMP WITH TIME ZONE,
    completed_at TIMESTAMP WITH TIME ZONE,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Task logs for detailed progress tracking
CREATE TABLE IF NOT EXISTS task_logs (
    id BIGSERIAL PRIMARY KEY,
    task_id UUID REFERENCES tasks(id) ON DELETE CASCADE,
    log_level TEXT NOT NULL DEFAULT 'info',
    message TEXT NOT NULL,
    details JSONB,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);
