-- SEO Analysis Tool - Database Indexes
-- This migration creates all indexes that exist in the current database

-- =====================================================
-- USER INDEXES
-- =====================================================

CREATE INDEX IF NOT EXISTS idx_users_email ON users(email);
CREATE INDEX IF NOT EXISTS idx_users_username ON users(username);
CREATE INDEX IF NOT EXISTS idx_users_is_active ON users(is_active);
CREATE INDEX IF NOT EXISTS idx_users_role ON users(role);

-- =====================================================
-- USER SESSION INDEXES
-- =====================================================

CREATE INDEX IF NOT EXISTS idx_user_sessions_user_id ON user_sessions(user_id);
CREATE INDEX IF NOT EXISTS idx_user_sessions_token_hash ON user_sessions(token_hash);
CREATE INDEX IF NOT EXISTS idx_user_sessions_expires_at ON user_sessions(expires_at);
CREATE INDEX IF NOT EXISTS idx_user_sessions_is_active ON user_sessions(is_active);

-- =====================================================
-- SITE INDEXES
-- =====================================================

CREATE INDEX IF NOT EXISTS idx_sites_user_id ON sites(user_id);
CREATE INDEX IF NOT EXISTS idx_sites_stats_last_updated ON sites(stats_last_updated);

-- =====================================================
-- PAGE INDEXES
-- =====================================================

CREATE INDEX IF NOT EXISTS idx_pages_site_id ON pages(site_id);
CREATE INDEX IF NOT EXISTS idx_pages_url ON pages("URL");
CREATE INDEX IF NOT EXISTS idx_pages_snapshot_date ON pages(snapshot_date);

-- =====================================================
-- GSC KEYWORDS INDEXES
-- =====================================================

CREATE INDEX IF NOT EXISTS idx_gsc_keywords_site_id ON gsc_keywords(site_id);
CREATE INDEX IF NOT EXISTS idx_gsc_keywords_url ON gsc_keywords("URL");
CREATE INDEX IF NOT EXISTS idx_gsc_keywords_month ON gsc_keywords("Month");

-- =====================================================
-- GSC TRAFFIC INDEXES
-- =====================================================

CREATE INDEX IF NOT EXISTS idx_gsc_traffic_site_id ON gsc_traffic(site_id);
CREATE INDEX IF NOT EXISTS idx_gsc_traffic_url ON gsc_traffic("URL");
CREATE INDEX IF NOT EXISTS idx_gsc_traffic_month ON gsc_traffic("Month");

-- =====================================================
-- GA DATA INDEXES
-- =====================================================

CREATE INDEX IF NOT EXISTS idx_ga_data_site_id ON ga_data(site_id);
CREATE INDEX IF NOT EXISTS idx_ga_data_url ON ga_data("URL");
CREATE INDEX IF NOT EXISTS idx_ga_data_month ON ga_data("Month");

-- =====================================================
-- INTERNAL LINKS INDEXES
-- =====================================================

CREATE INDEX IF NOT EXISTS idx_internal_links_site_id ON internal_links(site_id);
CREATE INDEX IF NOT EXISTS idx_internal_links_url ON internal_links("URL");
CREATE INDEX IF NOT EXISTS idx_internal_links_snapshot_date ON internal_links(snapshot_date);

-- =====================================================
-- TASK INDEXES
-- =====================================================

CREATE INDEX IF NOT EXISTS idx_tasks_site_id ON tasks(site_id);
CREATE INDEX IF NOT EXISTS idx_tasks_status ON tasks(status);
CREATE INDEX IF NOT EXISTS idx_tasks_priority ON tasks(priority);
CREATE INDEX IF NOT EXISTS idx_tasks_created_at ON tasks(created_at);
CREATE INDEX IF NOT EXISTS idx_tasks_user_session ON tasks(user_session);

-- =====================================================
-- TASK LOG INDEXES
-- =====================================================

CREATE INDEX IF NOT EXISTS idx_task_logs_task_id ON task_logs(task_id);
CREATE INDEX IF NOT EXISTS idx_task_logs_level ON task_logs(level);
CREATE INDEX IF NOT EXISTS idx_task_logs_created_at ON task_logs(created_at);
