-- SEO Analysis Tool - Optimized Database Indexes
-- Based on actual query patterns from codebase analysis

-- =====================================================
-- FOREIGN KEY INDEXES (Critical for JOINs)
-- =====================================================

CREATE INDEX IF NOT EXISTS idx_user_sessions_user_id ON user_sessions(user_id);
CREATE INDEX IF NOT EXISTS idx_sites_user_id ON sites(user_id);
CREATE INDEX IF NOT EXISTS idx_pages_site_id ON pages(site_id);
CREATE INDEX IF NOT EXISTS idx_gsc_keywords_site_id ON gsc_keywords(site_id);
CREATE INDEX IF NOT EXISTS idx_gsc_traffic_site_id ON gsc_traffic(site_id);
CREATE INDEX IF NOT EXISTS idx_ga_data_site_id ON ga_data(site_id);
CREATE INDEX IF NOT EXISTS idx_internal_links_site_id ON internal_links(site_id);
CREATE INDEX IF NOT EXISTS idx_tasks_site_id ON tasks(site_id);
CREATE INDEX IF NOT EXISTS idx_task_logs_task_id ON task_logs(task_id);

-- =====================================================
-- AUTHENTICATION INDEXES
-- =====================================================

CREATE INDEX IF NOT EXISTS idx_user_sessions_expires_at ON user_sessions(expires_at);
CREATE INDEX IF NOT EXISTS idx_user_sessions_token_hash ON user_sessions(token_hash);

-- =====================================================
-- TIME-BASED INDEXES (For latest data queries)
-- =====================================================

CREATE INDEX IF NOT EXISTS idx_pages_snapshot_date ON pages(snapshot_date);
CREATE INDEX IF NOT EXISTS idx_internal_links_snapshot_date ON internal_links(snapshot_date);
CREATE INDEX IF NOT EXISTS idx_tasks_created_at ON tasks(created_at);

-- =====================================================
-- TASK MANAGEMENT INDEXES
-- =====================================================

CREATE INDEX IF NOT EXISTS idx_tasks_status ON tasks(status);

-- =====================================================
-- COMPOSITE INDEXES (For complex queries)
-- =====================================================

-- For latest_pages view (most common query pattern)
CREATE INDEX IF NOT EXISTS idx_pages_site_url_date ON pages(site_id, "URL", snapshot_date DESC);

-- For link type filtering (internal vs external links)
CREATE INDEX IF NOT EXISTS idx_internal_links_site_type_date ON internal_links(site_id, "Link Type", snapshot_date);

-- For task queue processing
CREATE INDEX IF NOT EXISTS idx_tasks_status_created ON tasks(status, created_at DESC);

-- For user session management
CREATE INDEX IF NOT EXISTS idx_user_sessions_user_expires ON user_sessions(user_id, expires_at);

-- =====================================================
-- NOTES ON REMOVED INDEXES
-- =====================================================
-- Removed from current database (over-indexed):
-- - idx_users_email, idx_users_username (have unique constraints)
-- - idx_users_role, idx_users_is_active (low cardinality)
-- - idx_user_sessions_is_active (low cardinality)
-- - idx_task_logs_level (low cardinality)
-- - idx_tasks_priority (low cardinality)
-- - idx_*_url indexes (URLs rarely filtered independently)
-- - idx_*_month indexes (months rarely filtered independently)
-- - idx_sites_stats_last_updated (rarely queried)
-- - idx_tasks_user_session (rarely filtered)
