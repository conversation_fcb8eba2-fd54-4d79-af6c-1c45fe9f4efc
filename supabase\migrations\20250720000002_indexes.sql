-- SEO Analysis Tool - Essential Database Indexes
-- This migration creates only the necessary indexes for performance

-- =====================================================
-- ESSENTIAL INDEXES ONLY
-- =====================================================

-- Foreign key indexes (critical for JOIN performance)
CREATE INDEX IF NOT EXISTS idx_user_sessions_user_id ON user_sessions(user_id);
CREATE INDEX IF NOT EXISTS idx_sites_user_id ON sites(user_id);
CREATE INDEX IF NOT EXISTS idx_pages_site_id ON pages(site_id);
CREATE INDEX IF NOT EXISTS idx_gsc_keywords_site_id ON gsc_keywords(site_id);
CREATE INDEX IF NOT EXISTS idx_gsc_traffic_site_id ON gsc_traffic(site_id);
CREATE INDEX IF NOT EXISTS idx_ga_data_site_id ON ga_data(site_id);
CREATE INDEX IF NOT EXISTS idx_internal_links_site_id ON internal_links(site_id);
CREATE INDEX IF NOT EXISTS idx_tasks_site_id ON tasks(site_id);
CREATE INDEX IF NOT EXISTS idx_task_logs_task_id ON task_logs(task_id);

-- Authentication and session management (frequently queried)
CREATE INDEX IF NOT EXISTS idx_user_sessions_expires_at ON user_sessions(expires_at);
CREATE INDEX IF NOT EXISTS idx_users_is_active ON users(is_active);

-- Time-based queries (for latest data and cleanup)
CREATE INDEX IF NOT EXISTS idx_pages_snapshot_date ON pages(snapshot_date);
CREATE INDEX IF NOT EXISTS idx_internal_links_snapshot_date ON internal_links(snapshot_date);
CREATE INDEX IF NOT EXISTS idx_tasks_created_at ON tasks(created_at);

-- Task management (for queue processing)
CREATE INDEX IF NOT EXISTS idx_tasks_status ON tasks(status);

-- Composite index for latest pages view (most common query)
CREATE INDEX IF NOT EXISTS idx_pages_site_url_date ON pages(site_id, "URL", snapshot_date DESC);

-- Note: email and username already have unique constraints which create indexes
-- Note: URL and Month indexes removed - they're rarely queried independently
-- Note: Low-cardinality fields (role, priority, level) don't need indexes
-- Note: token_hash already has unique constraint
