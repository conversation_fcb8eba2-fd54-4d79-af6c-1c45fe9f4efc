#!/bin/bash

# SEO Analysis Tool - Supabase Setup Script
# This script automates the setup of the Supabase database for the SEO Analysis Tool

set -e  # Exit on any error

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Function to check if command exists
command_exists() {
    command -v "$1" >/dev/null 2>&1
}

# Function to check if Supabase CLI is installed
check_supabase_cli() {
    if ! command_exists supabase; then
        print_error "Supabase CLI is not installed."
        print_status "Please install it with: npm install -g supabase"
        print_status "Or visit: https://supabase.com/docs/guides/cli"
        exit 1
    fi
    print_success "Supabase CLI is installed"
}

# Function to check if project is linked
check_project_link() {
    if [ ! -f ".supabase/config.toml" ]; then
        print_error "Project is not linked to Supabase."
        print_status "Please run: supabase link --project-ref YOUR_PROJECT_REF"
        exit 1
    fi
    print_success "Project is linked to Supabase"
}

# Function to run migrations
run_migrations() {
    print_status "Running database migrations..."
    
    if supabase db push; then
        print_success "Migrations applied successfully"
    else
        print_error "Failed to apply migrations"
        exit 1
    fi
}

# Function to verify setup
verify_setup() {
    print_status "Verifying database setup..."
    
    # Check if we can connect to the database
    if supabase db reset --linked --debug; then
        print_success "Database connection verified"
    else
        print_error "Failed to connect to database"
        exit 1
    fi
}

# Function to show next steps
show_next_steps() {
    echo ""
    print_success "🎉 Supabase database setup complete!"
    echo ""
    print_status "Next steps:"
    echo "1. Update your .env file with Supabase credentials:"
    echo "   SUPABASE_URL=https://your-project-id.supabase.co"
    echo "   SUPABASE_KEY=your_supabase_anon_key"
    echo ""
    echo "2. Change the default admin password:"
    echo "   - Login to your Supabase dashboard"
    echo "   - Go to Authentication > Users"
    echo "   - Update <EMAIL> password"
    echo ""
    echo "3. Test your application:"
    echo "   python main.py"
    echo ""
    echo "4. Access your app at: http://localhost:8000"
    echo ""
    print_warning "Remember to remove sample data in production!"
}

# Main setup function
main() {
    echo ""
    print_status "🚀 Starting SEO Analysis Tool Supabase Setup"
    echo ""
    
    # Check prerequisites
    print_status "Checking prerequisites..."
    check_supabase_cli
    
    # Check if supabase is initialized
    if [ ! -f "supabase/config.toml" ]; then
        print_status "Initializing Supabase..."
        supabase init
    fi
    
    # Check project link
    check_project_link
    
    # Run migrations
    run_migrations
    
    # Verify setup
    verify_setup
    
    # Show next steps
    show_next_steps
}

# Handle script arguments
case "${1:-}" in
    --help|-h)
        echo "SEO Analysis Tool - Supabase Setup Script"
        echo ""
        echo "Usage: $0 [OPTIONS]"
        echo ""
        echo "Options:"
        echo "  --help, -h     Show this help message"
        echo "  --verify       Only verify the current setup"
        echo "  --reset        Reset and reapply all migrations"
        echo ""
        echo "Prerequisites:"
        echo "  - Supabase CLI installed (npm install -g supabase)"
        echo "  - Project linked to Supabase (supabase link --project-ref YOUR_REF)"
        echo ""
        exit 0
        ;;
    --verify)
        print_status "Verifying current setup..."
        check_supabase_cli
        check_project_link
        verify_setup
        print_success "Setup verification complete"
        exit 0
        ;;
    --reset)
        print_warning "This will reset your database and reapply all migrations."
        read -p "Are you sure? (y/N): " -n 1 -r
        echo
        if [[ $REPLY =~ ^[Yy]$ ]]; then
            print_status "Resetting database..."
            supabase db reset --linked
            print_success "Database reset complete"
        else
            print_status "Reset cancelled"
        fi
        exit 0
        ;;
    "")
        # No arguments, run main setup
        main
        ;;
    *)
        print_error "Unknown option: $1"
        print_status "Use --help for usage information"
        exit 1
        ;;
esac
