#!/usr/bin/env python3
"""
Simple health check script for Railway deployment
"""
import os
import sys
import time
import requests
from urllib.parse import urljoin

def check_health():
    """Check if the application is healthy"""
    port = os.getenv('PORT', '8000')
    base_url = f"http://localhost:{port}"
    health_url = urljoin(base_url, '/health')
    
    print(f"🏥 Checking health at: {health_url}")
    
    try:
        response = requests.get(health_url, timeout=10)
        
        if response.status_code == 200:
            data = response.json()
            print(f"✅ Health check passed: {data}")
            return True
        else:
            print(f"❌ Health check failed with status {response.status_code}: {response.text}")
            return False
            
    except requests.exceptions.ConnectionError:
        print("❌ Health check failed: Connection refused (app not ready)")
        return False
    except requests.exceptions.Timeout:
        print("❌ Health check failed: Timeout")
        return False
    except Exception as e:
        print(f"❌ Health check failed: {e}")
        return False

if __name__ == "__main__":
    # Wait a moment for the app to start
    time.sleep(2)
    
    # Try health check
    if check_health():
        sys.exit(0)  # Success
    else:
        sys.exit(1)  # Failure
