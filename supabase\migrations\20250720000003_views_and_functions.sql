-- SEO Analysis Tool - Views and Functions
-- This migration creates computed views and utility functions for efficient data access

-- =====================================================
-- COMPUTED VIEWS
-- =====================================================

-- Latest Pages View - Most recent snapshot of each page
CREATE OR REPLACE VIEW latest_pages AS
SELECT p.*
FROM pages p
INNER JOIN (
    SELECT site_id, "URL", MAX(snapshot_date) AS latest_date
    FROM pages
    GROUP BY site_id, "URL"
) latest ON p.site_id = latest.site_id 
    AND p."URL" = latest."URL" 
    AND p.snapshot_date = latest.latest_date;

-- Monthly Traffic Trends View - Domain-level traffic aggregations
CREATE OR REPLACE VIEW monthly_traffic_trends AS
SELECT 
    s.domain,
    gt."Month",
    COUNT(DISTINCT gt."URL") AS page_count,
    SUM(gt."Clicks") AS total_clicks,
    SUM(gt."Impressions") AS total_impressions,
    CASE 
        WHEN SUM(gt."Impressions") > 0 
        THEN SUM(gt."Clicks")::NUMERIC / SUM(gt."Impressions")::NUMERIC 
        ELSE 0 
    END AS avg_ctr,
    CASE 
        WHEN SUM(gt."Clicks") > 0 
        THEN SUM(gt."Position" * gt."Clicks") / SUM(gt."Clicks") 
        ELSE 0 
    END AS weighted_position
FROM gsc_traffic gt
INNER JOIN sites s ON gt.site_id = s.id
GROUP BY s.domain, gt."Month"
ORDER BY s.domain, gt."Month" DESC;

-- Top Pages View - Best performing pages across all metrics
CREATE OR REPLACE VIEW top_pages AS
SELECT 
    s.domain,
    p."URL",
    p."SEO Title",
    p."GSC Clicks",
    p."GSC Impressions",
    p."CTR",
    p."Position",
    p."Google Analytics Page Views",
    p.snapshot_date,
    -- Performance score calculation
    CASE 
        WHEN p."GSC Clicks" IS NULL THEN 0
        ELSE (
            COALESCE(p."GSC Clicks", 0) * 0.4 +
            COALESCE(p."Google Analytics Page Views", 0) * 0.3 +
            CASE WHEN p."Position" > 0 THEN (100 - p."Position") * 0.2 ELSE 0 END +
            COALESCE(p."CTR", 0) * 100 * 0.1
        )
    END AS performance_score
FROM latest_pages p
INNER JOIN sites s ON p.site_id = s.id
WHERE p."GSC Clicks" > 0 OR p."Google Analytics Page Views" > 0
ORDER BY performance_score DESC;

-- Site Summary View - Complete site statistics
CREATE OR REPLACE VIEW site_summary AS
SELECT 
    s.id,
    s.domain,
    s.created_at,
    s.last_analysis_date,
    s.user_id,
    -- Page statistics
    COUNT(DISTINCT lp."URL") AS total_pages,
    COUNT(DISTINCT CASE WHEN lp."GSC Clicks" > 0 THEN lp."URL" END) AS pages_with_traffic,
    -- Traffic statistics
    SUM(COALESCE(lp."GSC Clicks", 0)) AS total_clicks,
    SUM(COALESCE(lp."GSC Impressions", 0)) AS total_impressions,
    SUM(COALESCE(lp."Google Analytics Page Views", 0)) AS total_pageviews,
    -- Average metrics
    CASE 
        WHEN SUM(lp."GSC Impressions") > 0 
        THEN SUM(lp."GSC Clicks")::NUMERIC / SUM(lp."GSC Impressions")::NUMERIC 
        ELSE 0 
    END AS avg_ctr,
    CASE 
        WHEN COUNT(CASE WHEN lp."Position" > 0 THEN 1 END) > 0 
        THEN AVG(CASE WHEN lp."Position" > 0 THEN lp."Position" END) 
        ELSE 0 
    END AS avg_position,
    -- Link statistics
    COUNT(DISTINCT il.id) AS total_internal_links,
    COUNT(DISTINCT el.id) AS total_external_links,
    -- Keyword statistics
    COUNT(DISTINCT gk."Keyword") AS total_keywords
FROM sites s
LEFT JOIN latest_pages lp ON s.id = lp.site_id
LEFT JOIN internal_links il ON s.id = il.site_id AND il.snapshot_date = (
    SELECT MAX(snapshot_date) FROM internal_links WHERE site_id = s.id
)
LEFT JOIN external_links el ON s.id = el.site_id AND el.snapshot_date = (
    SELECT MAX(snapshot_date) FROM external_links WHERE site_id = s.id
)
LEFT JOIN gsc_keywords gk ON s.id = gk.site_id
GROUP BY s.id, s.domain, s.created_at, s.last_analysis_date, s.user_id;

-- =====================================================
-- UTILITY FUNCTIONS
-- =====================================================

-- Function to update site statistics
CREATE OR REPLACE FUNCTION update_site_stats(site_id_param BIGINT)
RETURNS VOID AS $$
BEGIN
    UPDATE sites SET
        stats_pages = (
            SELECT COUNT(DISTINCT "URL") 
            FROM latest_pages 
            WHERE site_id = site_id_param
        ),
        stats_keywords = (
            SELECT COUNT(DISTINCT "Keyword") 
            FROM gsc_keywords 
            WHERE site_id = site_id_param
        ),
        stats_internal_links = (
            SELECT COUNT(*) 
            FROM internal_links 
            WHERE site_id = site_id_param 
            AND snapshot_date = (
                SELECT MAX(snapshot_date) 
                FROM internal_links 
                WHERE site_id = site_id_param
            )
        ),
        stats_external_links = (
            SELECT COUNT(*) 
            FROM external_links 
            WHERE site_id = site_id_param 
            AND snapshot_date = (
                SELECT MAX(snapshot_date) 
                FROM external_links 
                WHERE site_id = site_id_param
            )
        ),
        stats_traffic_records = (
            SELECT COUNT(*) 
            FROM gsc_traffic 
            WHERE site_id = site_id_param
        ),
        stats_analytics_records = (
            SELECT COUNT(*) 
            FROM ga_data 
            WHERE site_id = site_id_param
        ),
        stats_last_updated = NOW()
    WHERE id = site_id_param;
END;
$$ LANGUAGE plpgsql;

-- Function to clean old task logs (keep last 1000 per task)
CREATE OR REPLACE FUNCTION cleanup_old_task_logs()
RETURNS VOID AS $$
BEGIN
    DELETE FROM task_logs 
    WHERE id NOT IN (
        SELECT id 
        FROM (
            SELECT id, ROW_NUMBER() OVER (PARTITION BY task_id ORDER BY created_at DESC) as rn
            FROM task_logs
        ) ranked 
        WHERE rn <= 1000
    );
END;
$$ LANGUAGE plpgsql;

-- Function to get site performance metrics
CREATE OR REPLACE FUNCTION get_site_performance(site_id_param BIGINT, months_back INTEGER DEFAULT 12)
RETURNS TABLE(
    month TEXT,
    total_clicks BIGINT,
    total_impressions BIGINT,
    avg_ctr NUMERIC,
    avg_position NUMERIC,
    total_pageviews BIGINT
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        gt."Month",
        SUM(gt."Clicks") AS total_clicks,
        SUM(gt."Impressions") AS total_impressions,
        CASE 
            WHEN SUM(gt."Impressions") > 0 
            THEN SUM(gt."Clicks")::NUMERIC / SUM(gt."Impressions")::NUMERIC 
            ELSE 0 
        END AS avg_ctr,
        CASE 
            WHEN SUM(gt."Clicks") > 0 
            THEN SUM(gt."Position" * gt."Clicks") / SUM(gt."Clicks") 
            ELSE 0 
        END AS avg_position,
        COALESCE(SUM(ga."Google Analytics Page Views"), 0) AS total_pageviews
    FROM gsc_traffic gt
    LEFT JOIN ga_data ga ON gt.site_id = ga.site_id 
        AND gt."URL" = ga."URL" 
        AND gt."Month" = ga."Month"
    WHERE gt.site_id = site_id_param
        AND gt."Month" >= TO_CHAR(NOW() - INTERVAL '%s months', 'YYYY-MM')
    GROUP BY gt."Month"
    ORDER BY gt."Month" DESC;
END;
$$ LANGUAGE plpgsql;
