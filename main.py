#!/usr/bin/env python3
"""
Main entry point for the SEO Analysis Tool
"""
import sys
import os

# Add src directory to Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from src.api.app import app

if __name__ == "__main__":
    import uvicorn
    from src.config.settings import settings

    # Use PORT environment variable for Railway deployment, fallback to settings
    port = int(os.getenv('PORT', settings.port or settings.api_port))

    # Railway requires 0.0.0.0 for public networking (Uvicorn doesn't support dual stack)
    # Force IPv4 for Railway deployment, ignore any IPv6 configuration
    host = "0.0.0.0"

    uvicorn.run(
        "main:app",
        host=host,
        port=port,
        reload=settings.debug
    )
