-- SEO Analysis Tool - Row Level Security (RLS) Policies
-- This migration sets up security policies for multi-user access control

-- =====================================================
-- ENABLE ROW LEVEL SECURITY
-- =====================================================

-- Enable RLS on all tables that need user-specific access control
ALTER TABLE sites ENABLE ROW LEVEL SECURITY;
ALTER TABLE pages ENABLE ROW LEVEL SECURITY;
ALTER TABLE gsc_keywords ENABLE ROW LEVEL SECURITY;
ALTER TABLE gsc_traffic ENABLE ROW LEVEL SECURITY;
ALTER TABLE ga_data ENABLE ROW LEVEL SECURITY;
ALTER TABLE internal_links ENABLE ROW LEVEL SECURITY;
ALTER TABLE external_links ENABLE ROW LEVEL SECURITY;
ALTER TABLE tasks ENABLE ROW LEVEL SECURITY;
ALTER TABLE task_logs ENABLE ROW LEVEL SECURITY;
ALTER TABLE users ENABLE ROW LEVEL SECURITY;
ALTER TABLE user_sessions ENABLE ROW LEVEL SECURITY;

-- =====================================================
-- HELPER FUNCTIONS FOR RLS
-- =====================================================

-- Function to get current user ID from JWT
CREATE OR REPLACE FUNCTION auth.user_id() 
RETURNS UUID AS $$
BEGIN
    RETURN COALESCE(
        auth.jwt() ->> 'sub',
        current_setting('request.jwt.claims', true)::json ->> 'sub'
    )::UUID;
EXCEPTION
    WHEN OTHERS THEN
        RETURN NULL::UUID;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to check if user is admin
CREATE OR REPLACE FUNCTION auth.is_admin() 
RETURNS BOOLEAN AS $$
BEGIN
    RETURN COALESCE(
        (auth.jwt() ->> 'role') = 'admin',
        (current_setting('request.jwt.claims', true)::json ->> 'role') = 'admin',
        false
    );
EXCEPTION
    WHEN OTHERS THEN
        RETURN false;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- =====================================================
-- USER MANAGEMENT POLICIES
-- =====================================================

-- Users can only see their own profile
CREATE POLICY "Users can view own profile" ON users
    FOR SELECT USING (id = auth.user_id());

-- Users can update their own profile
CREATE POLICY "Users can update own profile" ON users
    FOR UPDATE USING (id = auth.user_id());

-- Only admins can create users (or allow public registration)
CREATE POLICY "Admin can create users" ON users
    FOR INSERT WITH CHECK (auth.is_admin());

-- Users can manage their own sessions
CREATE POLICY "Users can view own sessions" ON user_sessions
    FOR SELECT USING (user_id = auth.user_id());

CREATE POLICY "Users can create own sessions" ON user_sessions
    FOR INSERT WITH CHECK (user_id = auth.user_id());

CREATE POLICY "Users can delete own sessions" ON user_sessions
    FOR DELETE USING (user_id = auth.user_id());

-- =====================================================
-- SITE MANAGEMENT POLICIES
-- =====================================================

-- Users can only access their own sites
CREATE POLICY "Users can view own sites" ON sites
    FOR SELECT USING (user_id = auth.user_id() OR auth.is_admin());

-- Users can create sites for themselves
CREATE POLICY "Users can create own sites" ON sites
    FOR INSERT WITH CHECK (user_id = auth.user_id());

-- Users can update their own sites
CREATE POLICY "Users can update own sites" ON sites
    FOR UPDATE USING (user_id = auth.user_id() OR auth.is_admin());

-- Users can delete their own sites
CREATE POLICY "Users can delete own sites" ON sites
    FOR DELETE USING (user_id = auth.user_id() OR auth.is_admin());

-- =====================================================
-- DATA ACCESS POLICIES
-- =====================================================

-- Pages: Users can only access data for their sites
CREATE POLICY "Users can view own site pages" ON pages
    FOR SELECT USING (
        site_id IN (
            SELECT id FROM sites WHERE user_id = auth.user_id()
        ) OR auth.is_admin()
    );

CREATE POLICY "Users can insert own site pages" ON pages
    FOR INSERT WITH CHECK (
        site_id IN (
            SELECT id FROM sites WHERE user_id = auth.user_id()
        )
    );

CREATE POLICY "Users can update own site pages" ON pages
    FOR UPDATE USING (
        site_id IN (
            SELECT id FROM sites WHERE user_id = auth.user_id()
        ) OR auth.is_admin()
    );

CREATE POLICY "Users can delete own site pages" ON pages
    FOR DELETE USING (
        site_id IN (
            SELECT id FROM sites WHERE user_id = auth.user_id()
        ) OR auth.is_admin()
    );

-- GSC Keywords: Users can only access data for their sites
CREATE POLICY "Users can view own site gsc_keywords" ON gsc_keywords
    FOR SELECT USING (
        site_id IN (
            SELECT id FROM sites WHERE user_id = auth.user_id()
        ) OR auth.is_admin()
    );

CREATE POLICY "Users can insert own site gsc_keywords" ON gsc_keywords
    FOR INSERT WITH CHECK (
        site_id IN (
            SELECT id FROM sites WHERE user_id = auth.user_id()
        )
    );

-- GSC Traffic: Users can only access data for their sites
CREATE POLICY "Users can view own site gsc_traffic" ON gsc_traffic
    FOR SELECT USING (
        site_id IN (
            SELECT id FROM sites WHERE user_id = auth.user_id()
        ) OR auth.is_admin()
    );

CREATE POLICY "Users can insert own site gsc_traffic" ON gsc_traffic
    FOR INSERT WITH CHECK (
        site_id IN (
            SELECT id FROM sites WHERE user_id = auth.user_id()
        )
    );

-- GA Data: Users can only access data for their sites
CREATE POLICY "Users can view own site ga_data" ON ga_data
    FOR SELECT USING (
        site_id IN (
            SELECT id FROM sites WHERE user_id = auth.user_id()
        ) OR auth.is_admin()
    );

CREATE POLICY "Users can insert own site ga_data" ON ga_data
    FOR INSERT WITH CHECK (
        site_id IN (
            SELECT id FROM sites WHERE user_id = auth.user_id()
        )
    );

-- Internal Links: Users can only access data for their sites
CREATE POLICY "Users can view own site internal_links" ON internal_links
    FOR SELECT USING (
        site_id IN (
            SELECT id FROM sites WHERE user_id = auth.user_id()
        ) OR auth.is_admin()
    );

CREATE POLICY "Users can insert own site internal_links" ON internal_links
    FOR INSERT WITH CHECK (
        site_id IN (
            SELECT id FROM sites WHERE user_id = auth.user_id()
        )
    );

-- External Links: Users can only access data for their sites
CREATE POLICY "Users can view own site external_links" ON external_links
    FOR SELECT USING (
        site_id IN (
            SELECT id FROM sites WHERE user_id = auth.user_id()
        ) OR auth.is_admin()
    );

CREATE POLICY "Users can insert own site external_links" ON external_links
    FOR INSERT WITH CHECK (
        site_id IN (
            SELECT id FROM sites WHERE user_id = auth.user_id()
        )
    );

-- =====================================================
-- TASK MANAGEMENT POLICIES
-- =====================================================

-- Users can only access their own tasks
CREATE POLICY "Users can view own tasks" ON tasks
    FOR SELECT USING (user_id = auth.user_id() OR auth.is_admin());

CREATE POLICY "Users can create own tasks" ON tasks
    FOR INSERT WITH CHECK (user_id = auth.user_id());

CREATE POLICY "Users can update own tasks" ON tasks
    FOR UPDATE USING (user_id = auth.user_id() OR auth.is_admin());

-- Users can view logs for their own tasks
CREATE POLICY "Users can view own task logs" ON task_logs
    FOR SELECT USING (
        task_id IN (
            SELECT id FROM tasks WHERE user_id = auth.user_id()
        ) OR auth.is_admin()
    );

CREATE POLICY "Users can insert own task logs" ON task_logs
    FOR INSERT WITH CHECK (
        task_id IN (
            SELECT id FROM tasks WHERE user_id = auth.user_id()
        )
    );

-- =====================================================
-- COMMENTS AND DOCUMENTATION
-- =====================================================

-- Add table comments for documentation
COMMENT ON TABLE sites IS 'Central registry of websites being analyzed with API configurations';
COMMENT ON TABLE pages IS 'Website page data and SEO metadata from crawling';
COMMENT ON TABLE gsc_keywords IS 'Google Search Console keyword performance data';
COMMENT ON TABLE gsc_traffic IS 'Aggregated GSC traffic by page and month';
COMMENT ON TABLE ga_data IS 'Google Analytics page performance metrics';
COMMENT ON TABLE internal_links IS 'Internal link analysis and anchor text data';
COMMENT ON TABLE external_links IS 'External link analysis and anchor text data';
COMMENT ON TABLE tasks IS 'Background task management for analysis jobs';
COMMENT ON TABLE task_logs IS 'Detailed logs for task progress tracking';
COMMENT ON TABLE users IS 'User accounts for multi-user access control';
COMMENT ON TABLE user_sessions IS 'User authentication sessions';
