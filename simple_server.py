#!/usr/bin/env python3
"""
Ultra-simple HTTP server for Railway debugging
"""
import os
import sys
from http.server import HTTPServer, BaseHTTPRequestHandler
import json

class SimpleHandler(BaseHTTPRequestHandler):
    def do_GET(self):
        if self.path == '/health':
            self.send_response(200)
            self.send_header('Content-type', 'application/json')
            self.end_headers()
            response = {"status": "healthy", "server": "simple"}
            self.wfile.write(json.dumps(response).encode())
        elif self.path == '/debug':
            self.send_response(200)
            self.send_header('Content-type', 'application/json')
            self.end_headers()
            response = {
                "python_version": sys.version,
                "working_dir": os.getcwd(),
                "files": os.listdir('.'),
                "environment": {k: v for k, v in os.environ.items() if 'SECRET' not in k.upper() and 'KEY' not in k.upper()}
            }
            self.wfile.write(json.dumps(response, indent=2).encode())
        else:
            self.send_response(200)
            self.send_header('Content-type', 'text/html')
            self.end_headers()
            html = f"""
            <html>
            <body>
                <h1>SEO Analysis Tool - Debug Mode</h1>
                <p>Server is running on Railway!</p>
                <p>Python: {sys.version}</p>
                <p>Working Directory: {os.getcwd()}</p>
                <p>PORT: {os.getenv('PORT', '8000')}</p>
                <ul>
                    <li><a href="/health">Health Check</a></li>
                    <li><a href="/debug">Debug Info</a></li>
                </ul>
            </body>
            </html>
            """
            self.wfile.write(html.encode())

if __name__ == "__main__":
    port = int(os.getenv('PORT', 8000))
    print(f"🚀 Starting simple server on port {port}")
    print(f"Python version: {sys.version}")
    print(f"Working directory: {os.getcwd()}")
    
    server = HTTPServer(('0.0.0.0', port), SimpleHandler)
    print(f"✅ Server started successfully on http://0.0.0.0:{port}")
    server.serve_forever()
