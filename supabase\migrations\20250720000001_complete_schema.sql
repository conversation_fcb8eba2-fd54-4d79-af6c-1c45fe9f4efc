-- SEO Analysis Tool - Complete Database Schema
-- This migration recreates the exact current database structure

-- Enable necessary extensions
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- =====================================================
-- USERS AND AUTHENTICATION
-- =====================================================

-- Users table
CREATE TABLE IF NOT EXISTS users (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    email VARCHAR(255) UNIQUE NOT NULL,
    username VARCHAR(100) UNIQUE NOT NULL,
    password_hash VARCHAR(255) NOT NULL,
    first_name VARCHAR(100),
    last_name VARCHAR(100),
    is_active BOOLEAN DEFAULT true,
    is_verified BOOLEAN DEFAULT false,
    role VARCHAR(50) DEFAULT 'user',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
    last_login TIMESTAMP WITH TIME ZONE,
    email_verified_at TIMESTAMP WITH TIME ZONE,
    password_reset_token VARCHAR(255),
    password_reset_expires TIMESTAMP WITH TIME ZONE,
    verification_token VARCHAR(255),
    verification_expires TIMESTAMP WITH TIME ZONE
);

-- User sessions table
CREATE TABLE IF NOT EXISTS user_sessions (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID REFERENCES users(id) ON DELETE CASCADE,
    token_hash VARCHAR(255) UNIQUE NOT NULL,
    refresh_token_hash VARCHAR(255),
    expires_at TIMESTAMP WITH TIME ZONE NOT NULL,
    refresh_expires_at TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
    last_used TIMESTAMP WITH TIME ZONE DEFAULT now(),
    ip_address INET,
    user_agent TEXT,
    is_active BOOLEAN DEFAULT true
);

-- =====================================================
-- CORE SEO ANALYSIS TABLES
-- =====================================================

-- Sites table
CREATE TABLE IF NOT EXISTS sites (
    id BIGSERIAL PRIMARY KEY,
    domain TEXT UNIQUE NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
    domain_property TEXT,
    ga_property_id TEXT,
    service_account_data JSONB,
    homepage TEXT,
    last_updated TIMESTAMP WITH TIME ZONE DEFAULT now(),
    wp_api_key TEXT,
    last_analysis_date TIMESTAMP WITH TIME ZONE,
    user_id UUID REFERENCES users(id) ON DELETE SET NULL,
    stats_pages INTEGER DEFAULT 0,
    stats_keywords INTEGER DEFAULT 0,
    stats_internal_links INTEGER DEFAULT 0,
    stats_traffic_records INTEGER DEFAULT 0,
    stats_external_links INTEGER DEFAULT 0,
    stats_last_updated TIMESTAMP WITH TIME ZONE DEFAULT now(),
    stats_analytics_records INTEGER DEFAULT 0
);

-- Pages table
CREATE TABLE IF NOT EXISTS pages (
    id BIGSERIAL PRIMARY KEY,
    site_id BIGINT REFERENCES sites(id) ON DELETE CASCADE,
    "URL" TEXT NOT NULL,
    snapshot_date DATE NOT NULL,
    "SEO Title" TEXT,
    "Meta Description" TEXT,
    "H1" TEXT,
    "Page Content" TEXT,
    "Focus Keyword" TEXT,
    "Page Type" TEXT,
    "Topic" TEXT,
    "Title Length" INTEGER,
    "GSC Clicks" INTEGER,
    "GSC Impressions" INTEGER,
    "Google Analytics Page Views" INTEGER,
    url_hash TEXT,
    content_hash TEXT,
    "CTR" NUMERIC,
    "Position" NUMERIC,
    UNIQUE(site_id, "URL", snapshot_date)
);

-- GSC Keywords table
CREATE TABLE IF NOT EXISTS gsc_keywords (
    id BIGSERIAL PRIMARY KEY,
    site_id BIGINT REFERENCES sites(id) ON DELETE CASCADE,
    "URL" TEXT NOT NULL,
    "Keyword" TEXT NOT NULL,
    "Month" TEXT NOT NULL,
    "Clicks" INTEGER,
    "Impressions" INTEGER,
    "CTR" NUMERIC,
    "Position" NUMERIC,
    keyword_hash TEXT,
    UNIQUE(site_id, "URL", "Keyword", "Month")
);

-- GSC Traffic table
CREATE TABLE IF NOT EXISTS gsc_traffic (
    id BIGSERIAL PRIMARY KEY,
    site_id BIGINT REFERENCES sites(id) ON DELETE CASCADE,
    "URL" TEXT NOT NULL,
    "Month" TEXT NOT NULL,
    "Clicks" INTEGER,
    "Impressions" INTEGER,
    "CTR" NUMERIC,
    "Position" NUMERIC,
    traffic_hash TEXT,
    UNIQUE(site_id, "URL", "Month")
);

-- GA Data table
CREATE TABLE IF NOT EXISTS ga_data (
    id BIGSERIAL PRIMARY KEY,
    site_id BIGINT REFERENCES sites(id) ON DELETE CASCADE,
    "URL" TEXT NOT NULL,
    "Month" TEXT NOT NULL,
    "Google Analytics Page Views" INTEGER,
    "Active Users" INTEGER,
    UNIQUE(site_id, "URL", "Month")
);

-- Internal Links table (includes all link types: internal, external, jump)
CREATE TABLE IF NOT EXISTS internal_links (
    id BIGSERIAL PRIMARY KEY,
    site_id BIGINT REFERENCES sites(id) ON DELETE CASCADE,
    "URL" TEXT NOT NULL,
    "Target Hyperlink" TEXT NOT NULL,
    "Anchor Text" TEXT,
    link_hash TEXT NOT NULL,
    snapshot_date DATE NOT NULL,
    "Link Type" TEXT,
    "URL Topic" TEXT,
    "Target Title" TEXT,
    "Relevance Score" NUMERIC,
    "Link Count" INTEGER DEFAULT 1,
    UNIQUE(site_id, link_hash, snapshot_date)
);

-- =====================================================
-- TASK MANAGEMENT
-- =====================================================

-- Tasks table
CREATE TABLE IF NOT EXISTS tasks (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    site_id BIGINT REFERENCES sites(id) ON DELETE CASCADE,
    task_type VARCHAR(50) NOT NULL,
    status VARCHAR(20) DEFAULT 'queued' NOT NULL,
    priority INTEGER DEFAULT 5,
    progress INTEGER DEFAULT 0,
    message TEXT,
    config JSONB,
    result JSONB,
    error_message TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
    started_at TIMESTAMP WITH TIME ZONE,
    completed_at TIMESTAMP WITH TIME ZONE,
    estimated_duration INTEGER,
    actual_duration INTEGER,
    retry_count INTEGER DEFAULT 0,
    max_retries INTEGER DEFAULT 3,
    user_session VARCHAR(255)
);

-- Task logs table
CREATE TABLE IF NOT EXISTS task_logs (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    task_id UUID REFERENCES tasks(id) ON DELETE CASCADE,
    level VARCHAR(10) NOT NULL,
    message TEXT NOT NULL,
    details JSONB,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT now()
);
