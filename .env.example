# SEO Analysis Tool - Environment Configuration
# Copy this file to .env and fill in your actual values

# ===================================
# SUPABASE CONFIGURATION (REQUIRED)
# ===================================
# Get these values from your Supabase project dashboard
SUPABASE_URL=https://your-project-id.supabase.co
SUPABASE_KEY=your_supabase_anon_key_here

# ===================================
# APPLICATION SETTINGS
# ===================================
# Port for the web server (default: 8000)
PORT=8000

# Environment (development, production)
ENVIRONMENT=development

# ===================================
# CRAWLING SETTINGS
# ===================================
# Maximum number of pages to crawl per site
MAX_CRAWL_PAGES=1000

# Crawling timeout in seconds
CRAWL_TIMEOUT=30

# JavaScript rendering timeout in milliseconds
JS_RENDER_TIMEOUT=60000

# Number of concurrent crawling workers
MAX_CONCURRENT_REQUESTS=5

# ===================================
# GOOGLE API SETTINGS
# ===================================
# Google API timeout in seconds
GOOGLE_API_TIMEOUT=60

# ===================================
# LOGGING
# ===================================
LOG_LEVEL=INFO
LOG_FORMAT=%(asctime)s - %(name)s - %(levelname)s - %(message)s

# ===================================
# FILE PATHS
# ===================================
REPORTS_DIR=reports
TEMP_DIR=temp
