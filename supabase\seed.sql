-- SEO Analysis Tool - Seed Data
-- This file contains initial data for setting up the application

-- =====================================================
-- INITIAL ADMIN USER (OPTIONAL)
-- =====================================================

-- Create a default admin user (password should be changed immediately)
-- Password: 'admin123' (hashed with bcrypt)
INSERT INTO users (id, email, username, password_hash, first_name, last_name, is_active, is_verified, role)
VALUES (
    '00000000-0000-0000-0000-000000000001'::UUID,
    '<EMAIL>',
    'admin',
    '$2b$12$LQv3c1yqBwEHxPuNYjHNTO.eeIFIXuxQOdvbQewQdBGrJiSQ0x9Pu', -- admin123
    'System',
    'Administrator',
    true,
    true,
    'admin'
) ON CONFLICT (email) DO NOTHING;

-- =====================================================
-- SAMPLE DATA (FOR TESTING - REMOVE IN PRODUCTION)
-- =====================================================

-- Sample site for testing (only if no sites exist)
INSERT INTO sites (
    id, 
    domain, 
    homepage, 
    domain_property, 
    ga_property_id,
    user_id,
    created_at
) 
SELECT 
    1,
    'example.com',
    'https://example.com',
    'https://example.com/',
    'GA4-PROPERTY-ID',
    '00000000-0000-0000-0000-000000000001'::UUID,
    NOW()
WHERE NOT EXISTS (SELECT 1 FROM sites LIMIT 1);

-- =====================================================
-- UTILITY FUNCTIONS FOR SETUP
-- =====================================================

-- Function to create a new user with hashed password
CREATE OR REPLACE FUNCTION create_user(
    user_email TEXT,
    user_username TEXT,
    user_password TEXT,
    user_first_name TEXT DEFAULT NULL,
    user_last_name TEXT DEFAULT NULL
)
RETURNS UUID AS $$
DECLARE
    new_user_id UUID;
    password_hash TEXT;
BEGIN
    -- Generate UUID for new user
    new_user_id := uuid_generate_v4();

    -- Hash the password (in production, this should be done by the application)
    -- This is a simplified example - use proper password hashing in your app
    password_hash := crypt(user_password, gen_salt('bf'));

    -- Insert the new user
    INSERT INTO users (id, email, username, password_hash, first_name, last_name, is_active, is_verified, role)
    VALUES (new_user_id, user_email, user_username, password_hash, user_first_name, user_last_name, true, false, 'user');

    RETURN new_user_id;
EXCEPTION
    WHEN unique_violation THEN
        RAISE EXCEPTION 'User with email % or username % already exists', user_email, user_username;
    WHEN OTHERS THEN
        RAISE EXCEPTION 'Error creating user: %', SQLERRM;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to initialize site statistics
CREATE OR REPLACE FUNCTION initialize_all_site_stats()
RETURNS VOID AS $$
DECLARE
    site_record RECORD;
BEGIN
    FOR site_record IN SELECT id FROM sites LOOP
        PERFORM update_site_stats(site_record.id);
    END LOOP;
END;
$$ LANGUAGE plpgsql;

-- =====================================================
-- INITIAL SETUP VERIFICATION
-- =====================================================

-- Function to verify database setup
CREATE OR REPLACE FUNCTION verify_database_setup()
RETURNS TABLE(
    table_name TEXT,
    row_count BIGINT,
    has_indexes BOOLEAN,
    has_constraints BOOLEAN
) AS $$
BEGIN
    RETURN QUERY
    WITH table_stats AS (
        SELECT 
            t.table_name,
            CASE t.table_name
                WHEN 'users' THEN (SELECT COUNT(*) FROM users)
                WHEN 'sites' THEN (SELECT COUNT(*) FROM sites)
                WHEN 'pages' THEN (SELECT COUNT(*) FROM pages)
                WHEN 'gsc_keywords' THEN (SELECT COUNT(*) FROM gsc_keywords)
                WHEN 'gsc_traffic' THEN (SELECT COUNT(*) FROM gsc_traffic)
                WHEN 'ga_data' THEN (SELECT COUNT(*) FROM ga_data)
                WHEN 'internal_links' THEN (SELECT COUNT(*) FROM internal_links)

                WHEN 'tasks' THEN (SELECT COUNT(*) FROM tasks)
                WHEN 'task_logs' THEN (SELECT COUNT(*) FROM task_logs)
                WHEN 'user_sessions' THEN (SELECT COUNT(*) FROM user_sessions)
                ELSE 0
            END AS row_count
        FROM information_schema.tables t
        WHERE t.table_schema = 'public' 
        AND t.table_type = 'BASE TABLE'
        AND t.table_name IN (
            'users', 'sites', 'pages', 'gsc_keywords', 'gsc_traffic', 
            'ga_data', 'internal_links', 'external_links', 'tasks', 
            'task_logs', 'user_sessions'
        )
    ),
    index_stats AS (
        SELECT 
            t.tablename,
            COUNT(i.indexname) > 0 AS has_indexes
        FROM pg_tables t
        LEFT JOIN pg_indexes i ON t.tablename = i.tablename
        WHERE t.schemaname = 'public'
        GROUP BY t.tablename
    ),
    constraint_stats AS (
        SELECT 
            tc.table_name,
            COUNT(tc.constraint_name) > 0 AS has_constraints
        FROM information_schema.table_constraints tc
        WHERE tc.table_schema = 'public'
        AND tc.constraint_type IN ('PRIMARY KEY', 'FOREIGN KEY', 'UNIQUE', 'CHECK')
        GROUP BY tc.table_name
    )
    SELECT 
        ts.table_name,
        ts.row_count,
        COALESCE(idx.has_indexes, false) AS has_indexes,
        COALESCE(cs.has_constraints, false) AS has_constraints
    FROM table_stats ts
    LEFT JOIN index_stats idx ON ts.table_name = idx.tablename
    LEFT JOIN constraint_stats cs ON ts.table_name = cs.table_name
    ORDER BY ts.table_name;
END;
$$ LANGUAGE plpgsql;

-- =====================================================
-- CLEANUP FUNCTIONS
-- =====================================================

-- Function to clean up old data (for maintenance)
CREATE OR REPLACE FUNCTION cleanup_old_data(days_to_keep INTEGER DEFAULT 90)
RETURNS VOID AS $$
BEGIN
    -- Clean up old task logs
    DELETE FROM task_logs 
    WHERE created_at < NOW() - INTERVAL '%s days' AND task_id IN (
        SELECT id FROM tasks WHERE status IN ('completed', 'failed', 'cancelled')
        AND completed_at < NOW() - INTERVAL '%s days'
    );
    
    -- Clean up old completed tasks
    DELETE FROM tasks 
    WHERE status IN ('completed', 'failed', 'cancelled')
    AND completed_at < NOW() - INTERVAL '%s days';
    
    -- Clean up expired user sessions
    DELETE FROM user_sessions 
    WHERE expires_at < NOW();
    
    RAISE NOTICE 'Cleanup completed. Removed data older than % days.', days_to_keep;
END;
$$ LANGUAGE plpgsql;

-- =====================================================
-- INITIAL DATA VERIFICATION
-- =====================================================

-- Verify that all required tables exist
DO $$
DECLARE
    missing_tables TEXT[];
    required_tables TEXT[] := ARRAY[
        'users', 'user_sessions', 'sites', 'pages', 'gsc_keywords',
        'gsc_traffic', 'ga_data', 'internal_links', 'tasks', 'task_logs'
    ];
    table_name TEXT;
BEGIN
    FOREACH table_name IN ARRAY required_tables LOOP
        IF NOT EXISTS (
            SELECT 1 FROM information_schema.tables 
            WHERE table_schema = 'public' AND table_name = table_name
        ) THEN
            missing_tables := array_append(missing_tables, table_name);
        END IF;
    END LOOP;
    
    IF array_length(missing_tables, 1) > 0 THEN
        RAISE EXCEPTION 'Missing required tables: %', array_to_string(missing_tables, ', ');
    ELSE
        RAISE NOTICE 'All required tables are present.';
    END IF;
END;
$$;

-- Verify that all required views exist
DO $$
DECLARE
    missing_views TEXT[];
    required_views TEXT[] := ARRAY['latest_pages', 'monthly_traffic_trends', 'top_pages'];
    view_name TEXT;
BEGIN
    FOREACH view_name IN ARRAY required_views LOOP
        IF NOT EXISTS (
            SELECT 1 FROM information_schema.views 
            WHERE table_schema = 'public' AND table_name = view_name
        ) THEN
            missing_views := array_append(missing_views, view_name);
        END IF;
    END LOOP;
    
    IF array_length(missing_views, 1) > 0 THEN
        RAISE EXCEPTION 'Missing required views: %', array_to_string(missing_views, ', ');
    ELSE
        RAISE NOTICE 'All required views are present.';
    END IF;
END;
$$;

-- Final setup message
DO $$
BEGIN
    RAISE NOTICE '=================================================';
    RAISE NOTICE 'SEO Analysis Tool Database Setup Complete!';
    RAISE NOTICE '=================================================';
    RAISE NOTICE 'Next steps:';
    RAISE NOTICE '1. Update the admin user password';
    RAISE NOTICE '2. Configure your application environment variables';
    RAISE NOTICE '3. Test the connection from your application';
    RAISE NOTICE '4. Remove sample data if not needed';
    RAISE NOTICE '=================================================';
END;
$$;
