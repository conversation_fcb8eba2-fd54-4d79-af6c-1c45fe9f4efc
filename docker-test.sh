#!/bin/bash

echo "🐳 Testing Docker container locally..."

# Build the Docker image
echo "📦 Building Docker image..."
docker build -t seo-analysis-tool .

if [ $? -ne 0 ]; then
    echo "❌ Docker build failed"
    exit 1
fi

echo "✅ Docker image built successfully"

# Run the container with environment variables
echo "🚀 Starting Docker container..."
docker run -d \
    --name seo-analysis-test \
    -p 8000:8000 \
    -e PORT=8000 \
    -e SUPABASE_URL="https://ltrymguxcxzyofxmbutv.supabase.co" \
    -e SUPABASE_KEY="eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************.bOwqh0pafQhZliiLL2Dp_BKsPC8-H5wm0YIlUa2SfFA" \
    seo-analysis-tool

if [ $? -ne 0 ]; then
    echo "❌ Docker run failed"
    exit 1
fi

echo "✅ Docker container started"
echo "🌐 Application should be available at: http://localhost:8000"
echo "🏥 Health check: http://localhost:8000/health"

# Wait a moment for startup
sleep 5

# Test health endpoint
echo "🔍 Testing health endpoint..."
curl -f http://localhost:8000/health

if [ $? -eq 0 ]; then
    echo "✅ Health check passed"
else
    echo "❌ Health check failed"
    echo "📋 Container logs:"
    docker logs seo-analysis-test
fi

echo ""
echo "🛠️  To view logs: docker logs seo-analysis-test"
echo "🛑 To stop: docker stop seo-analysis-test"
echo "🗑️  To remove: docker rm seo-analysis-test"
