#!/bin/bash

# Railway startup script with debugging
echo "🚀 Starting SEO Analysis Tool on Railway..."

# Print environment info
echo "📊 Environment Information:"
echo "PORT: ${PORT:-8000}"
echo "PYTHON_VERSION: $(python --version)"
echo "WORKING_DIR: $(pwd)"
echo "USER: $(whoami)"

# List files to verify deployment
echo "📁 Files in current directory:"
ls -la

# Check if main.py exists
if [ ! -f "main.py" ]; then
    echo "❌ ERROR: main.py not found!"
    exit 1
fi

# Create required directories
echo "📂 Creating required directories..."
mkdir -p reports temp logs public
chmod 755 reports temp logs public

# Check Python dependencies
echo "🐍 Checking Python dependencies..."
python -c "import fastapi; print('✅ FastAPI available')" || echo "❌ FastAPI not available"
python -c "import uvicorn; print('✅ Uvicorn available')" || echo "❌ Uvicorn not available"

# Start the application with detailed logging
echo "🌟 Starting application..."
exec uvicorn main:app \
    --host 0.0.0.0 \
    --port ${PORT:-8000} \
    --timeout-keep-alive 120 \
    --log-level info \
    --access-log
