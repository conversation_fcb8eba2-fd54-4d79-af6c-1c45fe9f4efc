"""
Task Queue Manager with priority-based scheduling and concurrency controls
"""
import asyncio
import threading
from datetime import datetime, timezone, timedelta
from typing import Dict, List, Optional, Any, Callable
from enum import Enum
from dataclasses import dataclass, field
from queue import PriorityQueue
import uuid

from src.services.task_manager import task_manager, TaskType, TaskStatus, TaskLogger
from src.utils.logging import get_logger

logger = get_logger(__name__)


class TaskPriority(Enum):
    """Task priority levels"""
    LOW = 1
    NORMAL = 2
    HIGH = 3
    URGENT = 4
    CRITICAL = 5


@dataclass
class QueuedTask:
    """Represents a task in the queue"""
    task_id: str
    task_type: TaskType
    priority: TaskPriority
    config: Dict[str, Any]
    user_id: str
    site_id: Optional[int] = None
    created_at: datetime = field(default_factory=lambda: datetime.now(timezone.utc))
    retry_count: int = 0
    max_retries: int = 3
    retry_delay: int = 60  # seconds
    next_retry_at: Optional[datetime] = None
    
    def __lt__(self, other):
        """For priority queue ordering (higher priority first)"""
        if self.priority.value != other.priority.value:
            return self.priority.value > other.priority.value
        return self.created_at < other.created_at


class TaskQueueManager:
    """Manages task queue with priority and concurrency controls"""
    
    def __init__(self, max_concurrent_tasks: int = 5):
        self.max_concurrent_tasks = max_concurrent_tasks
        self.running_tasks: Dict[str, asyncio.Task] = {}
        self.task_queue = PriorityQueue()
        self.retry_queue: List[QueuedTask] = []
        self.queue_lock = threading.Lock()
        self.is_running = False
        self.worker_task: Optional[asyncio.Task] = None
        
        # Task execution functions
        self.task_executors: Dict[TaskType, Callable] = {}
        
        logger.info(f"Task queue manager initialized with max {max_concurrent_tasks} concurrent tasks")
    
    def register_task_executor(self, task_type: TaskType, executor: Callable):
        """Register a task executor function for a specific task type"""
        self.task_executors[task_type] = executor
        logger.info(f"Registered executor for task type: {task_type.value}")
    
    def start(self):
        """Start the task queue worker"""
        if self.is_running:
            logger.warning("Task queue is already running")
            return
        
        self.is_running = True
        self.worker_task = asyncio.create_task(self._queue_worker())
        logger.info("Task queue worker started")
    
    def stop(self):
        """Stop the task queue worker"""
        if not self.is_running:
            return
        
        self.is_running = False
        if self.worker_task:
            self.worker_task.cancel()
        
        # Cancel all running tasks
        for task_id, task in self.running_tasks.items():
            task.cancel()
            logger.info(f"Cancelled running task: {task_id}")
        
        logger.info("Task queue worker stopped")
    
    def enqueue_task(
        self,
        task_type: TaskType,
        config: Dict[str, Any],
        user_id: str,
        site_id: Optional[int] = None,
        priority: Optional[TaskPriority] = None,
        max_retries: int = 3
    ) -> str:
        """Add a task to the queue"""

        # Auto-determine priority if not specified
        if priority is None:
            priority = self._determine_task_priority(task_type, user_id, config)

        # Create task in database first
        task_id = task_manager.create_task(
            task_type=task_type,
            config=config,
            estimated_duration=self._estimate_task_duration(task_type),
            priority=priority.value
        )
        
        # Create queued task
        queued_task = QueuedTask(
            task_id=task_id,
            task_type=task_type,
            priority=priority,
            config=config,
            user_id=user_id,
            site_id=site_id,
            max_retries=max_retries
        )
        
        # Add to queue
        with self.queue_lock:
            self.task_queue.put(queued_task)
        
        logger.info(f"Task {task_id} enqueued with priority {priority.name}")
        return task_id
    
    def get_queue_status(self) -> Dict[str, Any]:
        """Get current queue status"""
        with self.queue_lock:
            queue_size = self.task_queue.qsize()
            retry_queue_size = len(self.retry_queue)
        
        running_count = len(self.running_tasks)
        
        return {
            "queue_size": queue_size,
            "retry_queue_size": retry_queue_size,
            "running_tasks": running_count,
            "max_concurrent": self.max_concurrent_tasks,
            "available_slots": max(0, self.max_concurrent_tasks - running_count),
            "is_running": self.is_running
        }
    
    def get_user_queue_status(self, user_id: str) -> Dict[str, Any]:
        """Get queue status for a specific user"""
        # Count user's tasks in various states
        running_count = sum(1 for task_id in self.running_tasks.keys() 
                          if self._get_task_user_id(task_id) == user_id)
        
        # Count queued tasks for user (this is approximate since we can't easily iterate PriorityQueue)
        user_task_count = task_manager.get_user_task_count(user_id, TaskStatus.QUEUED)
        
        return {
            "user_id": user_id,
            "running_tasks": running_count,
            "queued_tasks": user_task_count,
            "total_active": running_count + user_task_count
        }
    
    async def _queue_worker(self):
        """Main queue worker loop"""
        logger.info("Queue worker started")

        # Wait 5 seconds before starting queue processing to avoid startup conflicts
        await asyncio.sleep(5)
        logger.info("Queue worker ready to process tasks")

        while self.is_running:
            try:
                # Process retry queue first
                await self._process_retry_queue()
                
                # Check if we can start new tasks
                if len(self.running_tasks) < self.max_concurrent_tasks:
                    await self._process_main_queue()
                
                # Clean up completed tasks
                await self._cleanup_completed_tasks()
                
                # Wait before next iteration
                await asyncio.sleep(1)
                
            except asyncio.CancelledError:
                logger.info("Queue worker cancelled")
                break
            except Exception as e:
                logger.error(f"Error in queue worker: {e}")
                await asyncio.sleep(5)  # Wait longer on error
        
        logger.info("Queue worker stopped")
    
    async def _process_retry_queue(self):
        """Process tasks in retry queue"""
        now = datetime.now(timezone.utc)
        ready_tasks = []
        
        with self.queue_lock:
            remaining_tasks = []
            for task in self.retry_queue:
                if task.next_retry_at and task.next_retry_at <= now:
                    ready_tasks.append(task)
                else:
                    remaining_tasks.append(task)
            self.retry_queue = remaining_tasks
        
        # Re-queue ready tasks
        for task in ready_tasks:
            with self.queue_lock:
                self.task_queue.put(task)
            logger.info(f"Task {task.task_id} moved from retry queue to main queue")
    
    async def _process_main_queue(self):
        """Process tasks from main queue"""
        if self.task_queue.empty():
            return
        
        try:
            with self.queue_lock:
                if not self.task_queue.empty():
                    queued_task = self.task_queue.get_nowait()
                else:
                    return
        except:
            return
        
        # Start the task
        await self._start_task(queued_task)
    
    async def _start_task(self, queued_task: QueuedTask):
        """Start executing a queued task"""
        task_id = queued_task.task_id

        try:
            # Check resource availability
            if not resource_monitor.check_resource_availability():
                logger.warning(f"Insufficient resources to start task {task_id}")
                # Put task back in queue
                with self.queue_lock:
                    self.task_queue.put(queued_task)
                return

            # Check concurrency limits
            if not concurrency_manager.can_start_task(queued_task.user_id, len(self.running_tasks)):
                logger.info(f"Concurrency limit reached for user {queued_task.user_id}, task {task_id} queued")
                # Put task back in queue
                with self.queue_lock:
                    self.task_queue.put(queued_task)
                return

            # Record task start
            concurrency_manager.task_started(queued_task.user_id)
            resource_monitor.record_task_start(task_id)

            # Update task status to running
            task_manager.update_task_progress(task_id, 0, "Task started", status=TaskStatus.RUNNING)

            # Get task executor
            executor = self.task_executors.get(queued_task.task_type)
            if not executor:
                raise ValueError(f"No executor registered for task type: {queued_task.task_type.value}")

            # Create and start async task
            async_task = asyncio.create_task(
                self._execute_task_with_error_handling(queued_task, executor)
            )

            self.running_tasks[task_id] = async_task
            logger.info(f"Started task {task_id} ({queued_task.task_type.value}) for user {queued_task.user_id}")

        except Exception as e:
            logger.error(f"Failed to start task {task_id}: {e}")
            concurrency_manager.task_finished(queued_task.user_id)
            await self._handle_task_failure(queued_task, str(e))
    
    async def _execute_task_with_error_handling(self, queued_task: QueuedTask, executor: Callable):
        """Execute task with error handling and retry logic"""
        task_id = queued_task.task_id
        
        try:
            # Execute the task
            result = await executor(queued_task.config, task_id)
            
            # Mark task as completed
            task_manager.update_task_progress(
                task_id, 100, "Task completed successfully", 
                status=TaskStatus.COMPLETED, result=result
            )
            
            logger.info(f"Task {task_id} completed successfully")
            
        except asyncio.CancelledError:
            logger.info(f"Task {task_id} was cancelled")
            task_manager.update_task_progress(
                task_id, 0, "Task cancelled", 
                status=TaskStatus.CANCELLED
            )
            
        except Exception as e:
            logger.error(f"Task {task_id} failed: {e}")
            await self._handle_task_failure(queued_task, str(e))
        
        finally:
            # Record task completion and cleanup
            success = task_manager.get_task(task_id).status == TaskStatus.COMPLETED
            resource_monitor.record_task_end(task_id, success)
            concurrency_manager.task_finished(queued_task.user_id)

            # Remove from running tasks
            if task_id in self.running_tasks:
                del self.running_tasks[task_id]
    
    async def _handle_task_failure(self, queued_task: QueuedTask, error_message: str):
        """Handle task failure with intelligent retry logic"""
        task_id = queued_task.task_id

        # Determine if error is retryable
        is_retryable = self._is_error_retryable(error_message)

        if is_retryable and queued_task.retry_count < queued_task.max_retries:
            # Schedule retry with exponential backoff and jitter
            queued_task.retry_count += 1
            base_delay = queued_task.retry_delay
            exponential_delay = base_delay * (2 ** (queued_task.retry_count - 1))

            # Add jitter to prevent thundering herd
            import random
            jitter = random.uniform(0.5, 1.5)
            final_delay = int(exponential_delay * jitter)

            # Cap maximum delay at 30 minutes
            final_delay = min(final_delay, 1800)

            queued_task.next_retry_at = datetime.now(timezone.utc) + timedelta(seconds=final_delay)

            with self.queue_lock:
                self.retry_queue.append(queued_task)

            task_manager.update_task_progress(
                task_id, 0,
                f"Task failed (retryable), retry {queued_task.retry_count}/{queued_task.max_retries} in {final_delay}s: {error_message}",
                status=TaskStatus.QUEUED
            )

            logger.info(f"Task {task_id} scheduled for retry {queued_task.retry_count}/{queued_task.max_retries} in {final_delay}s")

        else:
            # Mark as permanently failed
            failure_reason = "non-retryable error" if not is_retryable else f"max retries ({queued_task.max_retries}) exceeded"

            task_manager.update_task_progress(
                task_id, 0,
                f"Task failed permanently ({failure_reason}): {error_message}",
                status=TaskStatus.FAILED, error=error_message
            )

            logger.error(f"Task {task_id} failed permanently ({failure_reason}): {error_message}")

    def _is_error_retryable(self, error_message: str) -> bool:
        """Determine if an error is retryable based on error message"""
        error_lower = error_message.lower()

        # Non-retryable errors (permanent failures)
        non_retryable_patterns = [
            "authentication failed",
            "invalid credentials",
            "permission denied",
            "unauthorized",
            "forbidden",
            "not found",
            "invalid configuration",
            "malformed request",
            "validation error",
            "invalid email format",
            "password too weak"
        ]

        for pattern in non_retryable_patterns:
            if pattern in error_lower:
                return False

        # Retryable errors (temporary failures)
        retryable_patterns = [
            "timeout",
            "connection",
            "network",
            "temporary",
            "rate limit",
            "quota exceeded",
            "service unavailable",
            "internal server error",
            "bad gateway",
            "gateway timeout"
        ]

        for pattern in retryable_patterns:
            if pattern in error_lower:
                return True

        # Default to retryable for unknown errors
        return True
    
    async def _cleanup_completed_tasks(self):
        """Clean up completed async tasks"""
        completed_tasks = []
        for task_id, async_task in self.running_tasks.items():
            if async_task.done():
                completed_tasks.append(task_id)
        
        for task_id in completed_tasks:
            del self.running_tasks[task_id]
    
    def _determine_task_priority(self, task_type: TaskType, user_id: str, config: Dict[str, Any]) -> TaskPriority:
        """Automatically determine task priority based on various factors"""

        # Base priority by task type
        type_priority_map = {
            TaskType.ANALYSIS: TaskPriority.NORMAL,
            TaskType.REPORT_GENERATION: TaskPriority.HIGH,  # Reports are often time-sensitive
            TaskType.CRAWLING: TaskPriority.LOW,  # Crawling can be slower
            TaskType.DATA_EXPORT: TaskPriority.NORMAL,
        }

        base_priority = type_priority_map.get(task_type, TaskPriority.NORMAL)

        # Check user's current task load
        user_running_count = concurrency_manager.get_user_count(user_id)

        # If user has no running tasks, give higher priority
        if user_running_count == 0:
            if base_priority == TaskPriority.LOW:
                return TaskPriority.NORMAL
            elif base_priority == TaskPriority.NORMAL:
                return TaskPriority.HIGH

        # Check if this is a retry (lower priority for retries)
        if config.get("is_retry", False):
            if base_priority.value > 1:
                return TaskPriority(base_priority.value - 1)

        # Check queue size - if queue is getting full, prioritize faster tasks
        queue_status = self.get_queue_status()
        if queue_status["queue_size"] > 20:  # Queue getting full
            if task_type in [TaskType.DATA_EXPORT, TaskType.REPORT_GENERATION]:
                # Prioritize faster tasks
                return TaskPriority.HIGH

        return base_priority

    def update_task_priority(self, task_id: str, new_priority: TaskPriority) -> bool:
        """Update priority of a queued task"""
        try:
            # Update in database
            task_manager.supabase.table("tasks").update({
                "priority": new_priority.value
            }).eq("id", task_id).execute()

            # Find and update in queue (this is complex with PriorityQueue)
            # For now, we'll just update the database and let it take effect on next queue
            logger.info(f"Updated task {task_id} priority to {new_priority.name}")
            return True

        except Exception as e:
            logger.error(f"Failed to update task priority: {e}")
            return False

    def get_priority_stats(self) -> Dict[str, int]:
        """Get statistics about task priorities in queue"""
        # This would require iterating through the queue, which is not efficient
        # Instead, we'll get stats from the database
        try:
            result = task_manager.supabase.table("tasks").select(
                "priority", count="exact"
            ).eq("status", TaskStatus.QUEUED.value).execute()

            priority_counts = {}
            for priority in TaskPriority:
                priority_counts[priority.name] = 0

            # This is a simplified version - in practice you'd need to group by priority
            return priority_counts

        except Exception as e:
            logger.error(f"Failed to get priority stats: {e}")
            return {}

    def _estimate_task_duration(self, task_type: TaskType) -> int:
        """Estimate task duration in seconds"""
        duration_map = {
            TaskType.ANALYSIS: 300,  # 5 minutes
            TaskType.REPORT_GENERATION: 180,  # 3 minutes
            TaskType.CRAWLING: 600,  # 10 minutes
            TaskType.DATA_EXPORT: 120,  # 2 minutes
        }
        return duration_map.get(task_type, 300)
    
    def _get_task_user_id(self, task_id: str) -> Optional[str]:
        """Get user ID for a task (through site relationship)"""
        try:
            task = task_manager.get_task(task_id)
            if not task or not task.site_id:
                return None
            
            # Get site user_id
            from src.services.auth_service import auth_service
            site_result = auth_service.supabase.table("sites").select("user_id").eq("id", task.site_id).execute()
            
            if site_result.data:
                return site_result.data[0]["user_id"]
            
            return None
            
        except Exception as e:
            logger.error(f"Error getting task user ID: {e}")
            return None


class ResourceMonitor:
    """Monitor system resources and task performance"""

    def __init__(self):
        self.task_metrics: Dict[str, Dict[str, Any]] = {}
        self.resource_limits = {
            "max_memory_mb": 2048,  # 2GB
            "max_cpu_percent": 80,
            "max_concurrent_per_user": 3,
            "max_queue_size": 100
        }

    def check_resource_availability(self) -> bool:
        """Check if system has enough resources for new tasks"""
        try:
            import psutil

            # Check memory usage
            memory = psutil.virtual_memory()
            if memory.percent > 90:  # If memory usage > 90%
                logger.warning(f"High memory usage: {memory.percent}%")
                return False

            # Check CPU usage
            cpu_percent = psutil.cpu_percent(interval=1)
            if cpu_percent > self.resource_limits["max_cpu_percent"]:
                logger.warning(f"High CPU usage: {cpu_percent}%")
                return False

            return True

        except ImportError:
            # psutil not available, assume resources are OK
            logger.debug("psutil not available, skipping resource check")
            return True
        except Exception as e:
            logger.error(f"Error checking resources: {e}")
            return True  # Assume OK on error

    def record_task_start(self, task_id: str):
        """Record task start time and initial metrics"""
        self.task_metrics[task_id] = {
            "start_time": datetime.now(timezone.utc),
            "memory_start": self._get_memory_usage(),
            "cpu_start": self._get_cpu_usage()
        }

    def record_task_end(self, task_id: str, success: bool):
        """Record task completion metrics"""
        if task_id not in self.task_metrics:
            return

        metrics = self.task_metrics[task_id]
        end_time = datetime.now(timezone.utc)
        duration = (end_time - metrics["start_time"]).total_seconds()

        metrics.update({
            "end_time": end_time,
            "duration": duration,
            "memory_end": self._get_memory_usage(),
            "cpu_end": self._get_cpu_usage(),
            "success": success
        })

        logger.info(f"Task {task_id} metrics: duration={duration:.2f}s, success={success}")

    def get_task_metrics(self, task_id: str) -> Optional[Dict[str, Any]]:
        """Get metrics for a specific task"""
        return self.task_metrics.get(task_id)

    def cleanup_old_metrics(self, max_age_hours: int = 24):
        """Clean up old task metrics"""
        cutoff = datetime.now(timezone.utc) - timedelta(hours=max_age_hours)

        to_remove = []
        for task_id, metrics in self.task_metrics.items():
            if metrics.get("end_time", datetime.now(timezone.utc)) < cutoff:
                to_remove.append(task_id)

        for task_id in to_remove:
            del self.task_metrics[task_id]

        if to_remove:
            logger.info(f"Cleaned up metrics for {len(to_remove)} old tasks")

    def _get_memory_usage(self) -> float:
        """Get current memory usage in MB"""
        try:
            import psutil
            return psutil.virtual_memory().used / 1024 / 1024
        except:
            return 0.0

    def _get_cpu_usage(self) -> float:
        """Get current CPU usage percentage"""
        try:
            import psutil
            return psutil.cpu_percent()
        except:
            return 0.0


class ConcurrencyManager:
    """Manage concurrency limits per user and globally"""

    def __init__(self, global_limit: int = 5, per_user_limit: int = 3):
        self.global_limit = global_limit
        self.per_user_limit = per_user_limit
        self.user_task_counts: Dict[str, int] = {}
        self.lock = threading.Lock()

    def can_start_task(self, user_id: str, current_global_count: int) -> bool:
        """Check if a new task can be started for the user"""
        with self.lock:
            # Check global limit
            if current_global_count >= self.global_limit:
                return False

            # Check per-user limit
            user_count = self.user_task_counts.get(user_id, 0)
            if user_count >= self.per_user_limit:
                return False

            return True

    def task_started(self, user_id: str):
        """Record that a task has started for the user"""
        with self.lock:
            self.user_task_counts[user_id] = self.user_task_counts.get(user_id, 0) + 1

    def task_finished(self, user_id: str):
        """Record that a task has finished for the user"""
        with self.lock:
            if user_id in self.user_task_counts:
                self.user_task_counts[user_id] = max(0, self.user_task_counts[user_id] - 1)
                if self.user_task_counts[user_id] == 0:
                    del self.user_task_counts[user_id]

    def get_user_count(self, user_id: str) -> int:
        """Get current task count for user"""
        with self.lock:
            return self.user_task_counts.get(user_id, 0)

    def get_all_counts(self) -> Dict[str, int]:
        """Get task counts for all users"""
        with self.lock:
            return self.user_task_counts.copy()


# Global instances
resource_monitor = ResourceMonitor()
# Adjust these based on your server capacity
# Current: Conservative for safety
# Recommended for your hardware: global_limit=12, per_user_limit=5
concurrency_manager = ConcurrencyManager(global_limit=5, per_user_limit=3)
task_queue = TaskQueueManager(max_concurrent_tasks=5)
