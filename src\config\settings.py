"""
Configuration settings for the SEO Analysis Tool
"""
import os
from typing import Optional
from pydantic import Field
from pydantic_settings import BaseSettings


class Settings(BaseSettings):
    """Application settings with environment variable support"""
    
    # API Settings
    api_host: str = Field(default="0.0.0.0", env="API_HOST")
    api_port: int = Field(default=8000, env="API_PORT")
    port: int = Field(default=8000, env="PORT")  # Alternative port field
    debug: bool = Field(default=False, env="DEBUG")
    environment: str = Field(default="development", env="ENVIRONMENT")
    
    # File paths
    reports_dir: str = Field(default="reports", env="REPORTS_DIR")
    temp_dir: str = Field(default="temp", env="TEMP_DIR")
    
    # Google API Settings
    google_api_timeout: int = Field(default=60, env="GOOGLE_API_TIMEOUT")
    
    # Crawling Settings
    crawl_timeout: int = Field(default=10, env="CRAWL_TIMEOUT")
    js_render_timeout: int = Field(default=60000, env="JS_RENDER_TIMEOUT")
    max_concurrent_requests: int = Field(default=5, env="MAX_CONCURRENT_REQUESTS")
    max_crawl_pages: int = Field(default=1000, env="MAX_CRAWL_PAGES")
    
    # Supabase Settings (optional)
    supabase_url: Optional[str] = Field(default=None, env="SUPABASE_URL")
    supabase_key: Optional[str] = Field(default=None, env="SUPABASE_KEY")

    # Authentication Settings
    jwt_secret_key: str = Field(default="your-secret-key-change-in-production", env="JWT_SECRET_KEY")
    jwt_algorithm: str = Field(default="HS256", env="JWT_ALGORITHM")
    access_token_expire_minutes: int = Field(default=30, env="ACCESS_TOKEN_EXPIRE_MINUTES")
    refresh_token_expire_days: int = Field(default=7, env="REFRESH_TOKEN_EXPIRE_DAYS")
    
    # Logging
    log_level: str = Field(default="INFO", env="LOG_LEVEL")
    log_format: str = Field(
        default="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
        env="LOG_FORMAT"
    )
    
    model_config = {
        "env_file": ".env",
        "env_file_encoding": "utf-8",
        "extra": "ignore"  # Ignore extra fields in .env file
    }


# Global settings instance
settings = Settings()


# Validation functions
def validate_date_format(date_string: str) -> bool:
    """Validate date string format (YYYY-MM-DD)"""
    from datetime import datetime
    try:
        datetime.strptime(date_string, "%Y-%m-%d")
        return True
    except ValueError:
        return False


def ensure_directories():
    """Ensure required directories exist"""
    os.makedirs(settings.reports_dir, exist_ok=True)
    os.makedirs(settings.temp_dir, exist_ok=True)
