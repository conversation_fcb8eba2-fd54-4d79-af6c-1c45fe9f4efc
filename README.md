# SEO Analysis Tool

A comprehensive web application for analyzing websites, fetching Google Search Console and Google Analytics data, and generating detailed SEO reports. The tool provides a modern web interface for managing multiple sites and generating Excel reports with SEO insights.

## ✨ Features

- **Multi-Site Management**: Add, edit, and manage multiple websites from a single dashboard
- **Google Search Console Integration**: Fetch keyword performance, CTR, and position data
- **Google Analytics Integration**: Retrieve page views and user engagement metrics
- **WordPress API Integration**: Extract content and internal link data from WordPress sites
- **Web Crawling**: Intelligent crawling with JavaScript support for comprehensive page analysis
- **Excel Report Generation**: Professional reports with multiple sheets and formatted data
- **Supabase Database**: Secure cloud storage for all analysis data
- **Real-time Progress Tracking**: Monitor analysis progress with live updates

## 🚀 Project Structure

```
src/
├── api/                   # FastAPI web application
├── core/                  # Core business logic (crawling, Google APIs, WordPress)
├── services/              # High-level business services
├── database/              # Supabase database integration
├── models/                # Data models and schemas
├── config/                # Configuration management
└── utils/                 # Utility functions

public/                    # Web interface (HTML, CSS, JavaScript)
docs/                      # Documentation
migrations/                # Database migration scripts
wordpress/                 # WordPress plugin for data extraction
```

## 🚀 Quick Start

### Prerequisites

- Python 3.11 or higher
- Google Cloud Project with Search Console and Analytics APIs enabled
- Supabase account and project
- Google Service Account with appropriate permissions

### 1. Install Dependencies

```bash
pip install -r requirements.txt
```

### 2. Environment Setup

Create a `.env` file in the project root with your credentials:

```env
# Supabase Configuration
SUPABASE_URL=your_supabase_project_url
SUPABASE_KEY=your_supabase_anon_key

# Optional: Custom port (default: 8000)
PORT=8000
```

### 3. Google Service Account Setup

1. Create a Google Cloud Project
2. Enable Google Search Console API and Google Analytics API
3. Create a Service Account and download the JSON key file
4. Place the JSON file in the project root or upload via the web interface

### 4. Database Setup

#### Option A: Automated Setup (Recommended)

Run the setup script to automatically configure your Supabase database:

```bash
# Linux/Mac
./setup_supabase.sh

# Windows
setup_supabase.bat
```

#### Option B: Manual Setup

1. Install Supabase CLI: `npm install -g supabase`
2. Link your project: `supabase link --project-ref YOUR_PROJECT_REF`
3. Run migrations: `supabase db push`

The database will be automatically configured with all necessary tables, optimized indexes, and security policies.

### 5. Run the Application

```bash
python main.py
```

### 6. Access the Web Interface

Open your browser to: http://localhost:8000

## 🐳 Docker Deployment

### Build and Run with Docker

```bash
# Build the Docker image
docker build -t seo-analysis-tool .

# Run the container
docker run -p 8000:8000 --env-file .env seo-analysis-tool
```

### Using Docker Compose

Create a `docker-compose.yml` file:

```yaml
version: '3.8'
services:
  seo-tool:
    build: .
    ports:
      - "8000:8000"
    env_file:
      - .env
    volumes:
      - ./reports:/app/reports
```

Then run:

```bash
docker-compose up -d
```

## 📊 Using the Application

### Adding a Site

1. Click "Add New Site" in the web interface
2. Enter the domain name (e.g., "example.com")
3. Upload your Google Service Account JSON file
4. Configure Google Search Console property URL
5. Set Google Analytics property ID
6. Optionally configure WordPress API settings

### Running Analysis

1. Select a site from the dashboard
2. Click "Analyze" to start a new analysis
3. Monitor progress in real-time
4. Download Excel reports when complete

### Managing Sites

- **Edit**: Update site configuration and API settings
- **Re-analyze**: Run fresh analysis with latest data
- **Delete**: Remove site and all associated data
- **Download Reports**: Access previously generated Excel files

## 📋 Excel Reports

The application generates comprehensive Excel reports with multiple sheets:

- **Data Sheet**: Main page data with SEO metrics, Google Search Console data, and Google Analytics pageviews
- **Internal Links**: Internal link analysis with source and target pages
- **External Links**: External links found on the website
- **Summary**: High-level statistics and insights

## 🔧 Technical Details

### Supported Data Sources

- **Web Crawling**: BeautifulSoup and Playwright for comprehensive page analysis
- **Google Search Console**: Keyword performance, clicks, impressions, CTR, position
- **Google Analytics 4**: Page views and user engagement metrics
- **WordPress API**: Content extraction and internal link analysis

### Database Schema

The application uses Supabase with an optimized database structure (created by the migrations):

**Core Tables:**
- `sites`: Site configuration and API credentials
- `pages`: Crawled page data and SEO metrics
- `internal_links`: All link analysis (internal, external, and jump links with "Link Type" field)
- `gsc_keywords`: Google Search Console keyword performance data
- `gsc_traffic`: Aggregated GSC traffic by page and month
- `ga_data`: Google Analytics page performance metrics

**User Management:**
- `users`: User accounts and authentication
- `user_sessions`: Authentication session management

**Task Management:**
- `tasks`: Background job management for analysis
- `task_logs`: Detailed task progress tracking

**Computed Views:**
- `latest_pages`: Most recent page snapshots (optimized with composite indexes)
- `monthly_traffic_trends`: Domain-level traffic aggregations
- `top_pages`: Best performing pages

**Performance Features:**
- Optimized indexes based on actual query patterns (20 essential indexes)
- Composite indexes for complex queries (latest pages, link filtering, task processing)
- Row Level Security (RLS) for multi-user data isolation

### API Endpoints

- `GET /sites/`: List all sites
- `POST /sites/`: Add new site
- `PUT /sites/{id}`: Update site configuration
- `DELETE /sites/{id}`: Delete site
- `POST /sites/{id}/analyze`: Start analysis
- `GET /sites/{id}/report`: Download Excel report

For detailed API documentation, see [docs/API_DOCUMENTATION.md](docs/API_DOCUMENTATION.md)

## 🚀 Deployment

This project is configured for easy deployment on Render.com:

1. Push your code to GitHub
2. Connect your GitHub repository to Render.com
3. Set environment variables in Render dashboard
4. Deploy with the included `render.yaml` configuration

For detailed deployment instructions, see [docs/DEPLOYMENT_GUIDE.md](docs/DEPLOYMENT_GUIDE.md)

## 📚 Documentation

- [API Documentation](docs/API_DOCUMENTATION.md) - Complete API reference
- [Database Documentation](docs/SUPABASE_DATABASE_DOCUMENTATION.md) - Database schema and structure
- [Deployment Guide](docs/DEPLOYMENT_GUIDE.md) - Step-by-step deployment instructions

## 🔧 Troubleshooting

### Common Issues

1. **Google API Errors**: Ensure your service account has proper permissions for Search Console and Analytics
2. **Database Connection**: Verify Supabase URL and key in environment variables
3. **Crawling Issues**: Some sites may block automated crawling; check robots.txt and site policies
4. **Memory Issues**: For large sites, the application processes data in batches to manage memory usage

### Support

For technical support or questions about the application, refer to the documentation files or check the application logs for detailed error messages.

## 📄 License

This project is proprietary software developed for client use.
