@echo off
REM SEO Analysis Tool - Supabase Setup Script (Windows)
REM This script automates the setup of the Supabase database for the SEO Analysis Tool

setlocal enabledelayedexpansion

REM Check if Supabase CLI is installed
where supabase >nul 2>&1
if %errorlevel% neq 0 (
    echo [ERROR] Supabase CLI is not installed.
    echo Please install it with: npm install -g supabase
    echo Or visit: https://supabase.com/docs/guides/cli
    exit /b 1
)
echo [SUCCESS] Supabase CLI is installed

REM Check if project is linked
if not exist ".supabase\config.toml" (
    echo [ERROR] Project is not linked to Supabase.
    echo Please run: supabase link --project-ref YOUR_PROJECT_REF
    exit /b 1
)
echo [SUCCESS] Project is linked to Supabase

REM Initialize Supabase if needed
if not exist "supabase\config.toml" (
    echo [INFO] Initializing Supabase...
    supabase init
)

REM Run migrations
echo [INFO] Running database migrations...
supabase db push
if %errorlevel% neq 0 (
    echo [ERROR] Failed to apply migrations
    exit /b 1
)
echo [SUCCESS] Migrations applied successfully

REM Verify setup
echo [INFO] Verifying database setup...
supabase db reset --linked --debug
if %errorlevel% neq 0 (
    echo [ERROR] Failed to connect to database
    exit /b 1
)
echo [SUCCESS] Database connection verified

REM Show next steps
echo.
echo [SUCCESS] 🎉 Supabase database setup complete!
echo.
echo [INFO] Next steps:
echo 1. Update your .env file with Supabase credentials:
echo    SUPABASE_URL=https://your-project-id.supabase.co
echo    SUPABASE_KEY=your_supabase_anon_key
echo.
echo 2. Change the default admin password:
echo    - Login to your Supabase dashboard
echo    - Go to Authentication ^> Users
echo    - Update <EMAIL> password
echo.
echo 3. Test your application:
echo    python main.py
echo.
echo 4. Access your app at: http://localhost:8000
echo.
echo [WARNING] Remember to remove sample data in production!

pause
