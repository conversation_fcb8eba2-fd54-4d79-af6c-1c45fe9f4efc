-- SEO Analysis Tool - Indexes and Constraints
-- This migration adds all necessary indexes and constraints for optimal performance

-- =====================================================
-- UNIQUE CONSTRAINTS
-- =====================================================

-- Ensure unique combinations for data integrity
ALTER TABLE pages ADD CONSTRAINT IF NOT EXISTS unique_page_snapshot 
    UNIQUE (site_id, "URL", snapshot_date);

ALTER TABLE gsc_keywords ADD CONSTRAINT IF NOT EXISTS unique_gsc_keyword 
    UNIQUE (site_id, "URL", "Keyword", "Month");

ALTER TABLE gsc_traffic ADD CONSTRAINT IF NOT EXISTS unique_gsc_traffic 
    UNIQUE (site_id, "URL", "Month");

ALTER TABLE ga_data ADD CONSTRAINT IF NOT EXISTS unique_ga_data 
    UNIQUE (site_id, "URL", "Month");

ALTER TABLE internal_links ADD CONSTRAINT IF NOT EXISTS unique_internal_link 
    UNIQUE (site_id, link_hash, snapshot_date);



-- =====================================================
-- PERFORMANCE INDEXES
-- =====================================================

-- Sites table indexes
CREATE INDEX IF NOT EXISTS idx_sites_domain ON sites(domain);
CREATE INDEX IF NOT EXISTS idx_sites_user_id ON sites(user_id);
CREATE INDEX IF NOT EXISTS idx_sites_domain_property ON sites(domain_property);
CREATE INDEX IF NOT EXISTS idx_sites_ga_property_id ON sites(ga_property_id);
CREATE INDEX IF NOT EXISTS idx_sites_last_analysis_date ON sites(last_analysis_date);
CREATE INDEX IF NOT EXISTS idx_sites_stats_last_updated ON sites(stats_last_updated);

-- Pages table indexes
CREATE INDEX IF NOT EXISTS idx_pages_site_id ON pages(site_id);
CREATE INDEX IF NOT EXISTS idx_pages_url ON pages("URL");
CREATE INDEX IF NOT EXISTS idx_pages_snapshot_date ON pages(snapshot_date);
CREATE INDEX IF NOT EXISTS idx_pages_url_hash ON pages(url_hash);
CREATE INDEX IF NOT EXISTS idx_pages_content_hash ON pages(content_hash);
CREATE INDEX IF NOT EXISTS idx_pages_ctr ON pages("CTR");
CREATE INDEX IF NOT EXISTS idx_pages_position ON pages("Position");
CREATE INDEX IF NOT EXISTS idx_pages_gsc_clicks ON pages("GSC Clicks");
CREATE INDEX IF NOT EXISTS idx_pages_gsc_impressions ON pages("GSC Impressions");
CREATE INDEX IF NOT EXISTS idx_pages_ga_pageviews ON pages("Google Analytics Page Views");

-- GSC Keywords table indexes
CREATE INDEX IF NOT EXISTS idx_gsc_keywords_site_id ON gsc_keywords(site_id);
CREATE INDEX IF NOT EXISTS idx_gsc_keywords_url ON gsc_keywords("URL");
CREATE INDEX IF NOT EXISTS idx_gsc_keywords_keyword ON gsc_keywords("Keyword");
CREATE INDEX IF NOT EXISTS idx_gsc_keywords_month ON gsc_keywords("Month");
CREATE INDEX IF NOT EXISTS idx_gsc_keywords_hash ON gsc_keywords(keyword_hash);
CREATE INDEX IF NOT EXISTS idx_gsc_keywords_clicks ON gsc_keywords("Clicks");
CREATE INDEX IF NOT EXISTS idx_gsc_keywords_impressions ON gsc_keywords("Impressions");

-- GSC Traffic table indexes
CREATE INDEX IF NOT EXISTS idx_gsc_traffic_site_id ON gsc_traffic(site_id);
CREATE INDEX IF NOT EXISTS idx_gsc_traffic_url ON gsc_traffic("URL");
CREATE INDEX IF NOT EXISTS idx_gsc_traffic_month ON gsc_traffic("Month");
CREATE INDEX IF NOT EXISTS idx_gsc_traffic_hash ON gsc_traffic(traffic_hash);

-- GA Data table indexes
CREATE INDEX IF NOT EXISTS idx_ga_data_site_id ON ga_data(site_id);
CREATE INDEX IF NOT EXISTS idx_ga_data_url ON ga_data("URL");
CREATE INDEX IF NOT EXISTS idx_ga_data_month ON ga_data("Month");

-- Internal Links table indexes (includes all link types: internal, external, jump)
CREATE INDEX IF NOT EXISTS idx_internal_links_site_id ON internal_links(site_id);
CREATE INDEX IF NOT EXISTS idx_internal_links_url ON internal_links("URL");
CREATE INDEX IF NOT EXISTS idx_internal_links_target ON internal_links("Target Hyperlink");
CREATE INDEX IF NOT EXISTS idx_internal_links_hash ON internal_links(link_hash);
CREATE INDEX IF NOT EXISTS idx_internal_links_snapshot_date ON internal_links(snapshot_date);
CREATE INDEX IF NOT EXISTS idx_internal_links_link_type ON internal_links("Link Type");
CREATE INDEX IF NOT EXISTS idx_internal_links_relevance_score ON internal_links("Relevance Score");



-- User and session indexes
CREATE INDEX IF NOT EXISTS idx_users_email ON users(email);
CREATE INDEX IF NOT EXISTS idx_users_username ON users(username);
CREATE INDEX IF NOT EXISTS idx_users_is_active ON users(is_active);
CREATE INDEX IF NOT EXISTS idx_users_is_verified ON users(is_verified);
CREATE INDEX IF NOT EXISTS idx_users_role ON users(role);
CREATE INDEX IF NOT EXISTS idx_user_sessions_user_id ON user_sessions(user_id);
CREATE INDEX IF NOT EXISTS idx_user_sessions_token_hash ON user_sessions(token_hash);
CREATE INDEX IF NOT EXISTS idx_user_sessions_expires_at ON user_sessions(expires_at);

-- Task management indexes
CREATE INDEX IF NOT EXISTS idx_tasks_site_id ON tasks(site_id);
CREATE INDEX IF NOT EXISTS idx_tasks_user_id ON tasks(user_id);
CREATE INDEX IF NOT EXISTS idx_tasks_status ON tasks(status);
CREATE INDEX IF NOT EXISTS idx_tasks_task_type ON tasks(task_type);
CREATE INDEX IF NOT EXISTS idx_tasks_created_at ON tasks(created_at);
CREATE INDEX IF NOT EXISTS idx_tasks_updated_at ON tasks(updated_at);

CREATE INDEX IF NOT EXISTS idx_task_logs_task_id ON task_logs(task_id);
CREATE INDEX IF NOT EXISTS idx_task_logs_log_level ON task_logs(log_level);
CREATE INDEX IF NOT EXISTS idx_task_logs_created_at ON task_logs(created_at);

-- =====================================================
-- COMPOSITE INDEXES FOR COMPLEX QUERIES
-- =====================================================

-- For dashboard queries
CREATE INDEX IF NOT EXISTS idx_pages_site_latest ON pages(site_id, "URL", snapshot_date DESC);
CREATE INDEX IF NOT EXISTS idx_gsc_keywords_site_month ON gsc_keywords(site_id, "Month");
CREATE INDEX IF NOT EXISTS idx_gsc_traffic_site_month ON gsc_traffic(site_id, "Month");
CREATE INDEX IF NOT EXISTS idx_ga_data_site_month ON ga_data(site_id, "Month");

-- For link analysis
CREATE INDEX IF NOT EXISTS idx_internal_links_site_date ON internal_links(site_id, snapshot_date);
CREATE INDEX IF NOT EXISTS idx_external_links_site_date ON external_links(site_id, snapshot_date);

-- For user-specific queries
CREATE INDEX IF NOT EXISTS idx_sites_user_domain ON sites(user_id, domain);
CREATE INDEX IF NOT EXISTS idx_tasks_user_status ON tasks(user_id, status);

-- =====================================================
-- CHECK CONSTRAINTS
-- =====================================================

-- Ensure valid data ranges
ALTER TABLE pages ADD CONSTRAINT IF NOT EXISTS check_title_length_positive 
    CHECK ("Title Length" >= 0);

ALTER TABLE gsc_keywords ADD CONSTRAINT IF NOT EXISTS check_gsc_clicks_positive 
    CHECK ("Clicks" >= 0);

ALTER TABLE gsc_keywords ADD CONSTRAINT IF NOT EXISTS check_gsc_impressions_positive 
    CHECK ("Impressions" >= 0);

ALTER TABLE gsc_keywords ADD CONSTRAINT IF NOT EXISTS check_gsc_ctr_valid 
    CHECK ("CTR" >= 0 AND "CTR" <= 1);

ALTER TABLE gsc_keywords ADD CONSTRAINT IF NOT EXISTS check_gsc_position_positive 
    CHECK ("Position" > 0);

-- Task status constraints
ALTER TABLE tasks ADD CONSTRAINT IF NOT EXISTS check_task_status_valid 
    CHECK (status IN ('pending', 'running', 'completed', 'failed', 'cancelled'));

ALTER TABLE tasks ADD CONSTRAINT IF NOT EXISTS check_task_progress_valid 
    CHECK (progress >= 0 AND progress <= 100);

ALTER TABLE task_logs ADD CONSTRAINT IF NOT EXISTS check_log_level_valid 
    CHECK (log_level IN ('debug', 'info', 'warning', 'error', 'critical'));
