# SEO Analysis Tool - Supabase Database Setup

This directory contains all the necessary migrations and configuration files to set up the SEO Analysis Tool database on a new Supabase project.

## 📋 Prerequisites

- Supabase CLI installed (`npm install -g supabase`)
- A Supabase project (create at [supabase.com](https://supabase.com))
- Project URL and service role key from your Supabase dashboard

## 🚀 Quick Setup

### 1. Initialize Supabase (if not already done)

```bash
# In your project root directory
supabase init
```

### 2. Link to Your Supabase Project

```bash
# Replace with your project reference ID
supabase link --project-ref YOUR_PROJECT_REF
```

### 3. Run Migrations

```bash
# Apply all migrations to your remote database
supabase db push
```

### 4. Verify Setup

```bash
# Check migration status
supabase migration list

# Verify database setup
supabase db reset --linked
```

## 📁 Migration Files

### `20250720000001_initial_schema.sql`
- Creates all core tables (users, sites, pages, gsc_keywords, etc.)
- Sets up foreign key relationships
- Establishes basic table structure

### `20250720000002_indexes_and_constraints.sql`
- Adds performance indexes for all tables
- Creates unique constraints for data integrity
- Sets up check constraints for data validation

### `20250720000003_views_and_functions.sql`
- Creates computed views (latest_pages, monthly_traffic_trends, etc.)
- Adds utility functions for statistics and maintenance
- Sets up performance optimization functions

### `20250720000004_row_level_security.sql`
- Enables Row Level Security (RLS) on all tables
- Creates security policies for multi-user access
- Sets up user authentication and authorization

### `seed.sql`
- Provides initial data and setup verification
- Creates utility functions for user management
- Includes sample data for testing (remove in production)

## 🗄️ Database Schema Overview

### Core Tables

1. **users** - User accounts and authentication
2. **user_sessions** - Authentication session management
3. **sites** - Website registry and configuration
4. **pages** - Crawled page data and SEO metadata
5. **gsc_keywords** - Google Search Console keyword data
6. **gsc_traffic** - Aggregated GSC traffic by page/month
7. **ga_data** - Google Analytics page performance
8. **internal_links** - Internal link analysis
9. **external_links** - External link analysis
10. **tasks** - Background job management
11. **task_logs** - Detailed task progress tracking

### Computed Views

1. **latest_pages** - Most recent page snapshots
2. **monthly_traffic_trends** - Domain-level traffic aggregations
3. **top_pages** - Best performing pages
4. **site_summary** - Complete site statistics

## 🔐 Security Features

- **Row Level Security (RLS)** enabled on all tables
- **User-specific data access** - users can only see their own data
- **Admin override** capabilities for system administration
- **JWT-based authentication** integration
- **Secure password hashing** with bcrypt

## 🛠️ Manual Setup (Alternative)

If you prefer to run migrations manually:

1. **Copy migration contents** to Supabase SQL Editor
2. **Run in order**: 
   - initial_schema.sql
   - indexes_and_constraints.sql
   - views_and_functions.sql
   - row_level_security.sql
3. **Run seed.sql** for initial setup
4. **Verify setup** using the verification functions

## 📊 Environment Variables

After setting up the database, configure these environment variables in your application:

```env
# Required Supabase Configuration
SUPABASE_URL=https://your-project-id.supabase.co
SUPABASE_KEY=your_supabase_anon_key

# Optional Configuration
MAX_CRAWL_PAGES=1000
CRAWL_TIMEOUT=30
MAX_CONCURRENT_REQUESTS=5
```

## 🧪 Testing the Setup

### Verify Database Structure

```sql
-- Run this in Supabase SQL Editor
SELECT * FROM verify_database_setup();
```

### Test User Creation

```sql
-- Create a test user
SELECT create_user('<EMAIL>', 'password123', 'Test User');
```

### Check Site Statistics

```sql
-- Initialize statistics for all sites
SELECT initialize_all_site_stats();
```

## 🔧 Maintenance Functions

### Update Site Statistics

```sql
-- Update stats for a specific site
SELECT update_site_stats(1);

-- Update stats for all sites
SELECT initialize_all_site_stats();
```

### Clean Up Old Data

```sql
-- Remove data older than 90 days
SELECT cleanup_old_data(90);

-- Clean up old task logs
SELECT cleanup_old_task_logs();
```

## 🚨 Important Notes

### Security Considerations

1. **Change default admin password** immediately after setup
2. **Remove sample data** in production environments
3. **Configure proper JWT settings** in Supabase Auth
4. **Set up proper CORS policies** for your domain

### Performance Optimization

1. **Monitor query performance** using Supabase dashboard
2. **Add additional indexes** if needed for your specific use case
3. **Run cleanup functions** regularly to maintain performance
4. **Consider partitioning** large tables if you have high data volume

### Backup and Recovery

1. **Enable automatic backups** in Supabase dashboard
2. **Test restore procedures** regularly
3. **Export schema** periodically for version control
4. **Monitor database size** and plan for scaling

## 📞 Support

If you encounter issues during setup:

1. Check the Supabase logs in your dashboard
2. Verify all environment variables are correct
3. Ensure your Supabase project has sufficient resources
4. Review the migration files for any syntax errors

## 🔄 Migration Management

### Creating New Migrations

```bash
# Create a new migration file
supabase migration new your_migration_name

# Edit the generated file in supabase/migrations/
# Then apply it
supabase db push
```

### Rolling Back Migrations

```bash
# Reset to a specific migration
supabase db reset --linked

# Or reset completely and reapply
supabase db reset --linked --debug
```

---

**Database setup complete!** Your SEO Analysis Tool is now ready to use with a fully configured Supabase backend.
