-- SEO Analysis Tool - Database Views
-- This migration creates the views that exist in the current database

-- =====================================================
-- LATEST PAGES VIEW
-- =====================================================

CREATE OR REPLACE VIEW latest_pages AS
SELECT 
    p.id,
    p.site_id,
    p."URL",
    p.snapshot_date,
    p."SEO Title",
    p."Meta Description",
    p."H1",
    p."Page Content",
    p."Focus Keyword",
    p."Page Type",
    p."Topic",
    p."Title Length",
    p."GSC Clicks",
    p."GSC Impressions",
    p."Google Analytics Page Views"
FROM pages p
JOIN (
    SELECT 
        site_id,
        "URL",
        MAX(snapshot_date) AS latest_date
    FROM pages
    GROUP BY site_id, "URL"
) latest ON (
    p.site_id = latest.site_id 
    AND p."URL" = latest."URL" 
    AND p.snapshot_date = latest.latest_date
);

-- =====================================================
-- MONTHLY TRAFFIC TRENDS VIEW
-- =====================================================

CREATE OR REPLACE VIEW monthly_traffic_trends AS
SELECT 
    s.domain,
    t."Month",
    COUNT(DISTINCT t."URL") AS page_count,
    SUM(t."Clicks") AS total_clicks,
    SUM(t."Impressions") AS total_impressions,
    CASE
        WHEN SUM(t."Impressions") > 0 
        THEN SUM(t."Clicks")::DOUBLE PRECISION / SUM(t."Impressions")::DOUBLE PRECISION
        ELSE 0::DOUBLE PRECISION
    END AS avg_ctr,
    SUM(t."Clicks"::NUMERIC * t."Position") / NULLIF(SUM(t."Clicks"), 0)::NUMERIC AS weighted_position
FROM gsc_traffic t
JOIN sites s ON t.site_id = s.id
GROUP BY s.domain, t."Month"
ORDER BY s.domain, t."Month";

-- =====================================================
-- TOP PAGES VIEW
-- =====================================================

CREATE OR REPLACE VIEW top_pages AS
SELECT 
    s.domain,
    p."URL",
    p."SEO Title",
    p."H1",
    SUM(g."Google Analytics Page Views") AS total_pageviews,
    SUM(t."Clicks") AS total_clicks,
    SUM(t."Impressions") AS total_impressions
FROM pages p
JOIN sites s ON p.site_id = s.id
LEFT JOIN gsc_traffic t ON p.site_id = t.site_id AND p."URL" = t."URL"
LEFT JOIN ga_data g ON p.site_id = g.site_id AND p."URL" = g."URL"
WHERE p.snapshot_date = (
    SELECT MAX(snapshot_date) 
    FROM pages 
    WHERE site_id = p.site_id
)
GROUP BY s.domain, p."URL", p."SEO Title", p."H1"
ORDER BY SUM(t."Clicks") DESC NULLS LAST;
