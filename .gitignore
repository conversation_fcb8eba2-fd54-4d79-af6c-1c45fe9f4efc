# SEO Analysis Tool - .gitignore

# ===================================
# SENSITIVE DATA & CREDENTIALS
# ===================================

# Google Service Account Keys (JSON files with private keys)
*.json
!config_example_no_supabase.json
!package.json
!package-lock.json

# Environment variables and configuration files
.env
.env.local
.env.production
.env.staging
*.env

# API Keys and secrets
*config.json
*-config.json
wp-*-config.json
*credentials*
*secret*
*key*
service_account.json
credentials.json

# Supabase configuration (contains sensitive URLs/keys)
supabase/.env
supabase/.env.*

# ===================================
# PYTHON
# ===================================

# Byte-compiled / optimized / DLL files
__pycache__/
*.py[cod]
*$py.class

# C extensions
*.so

# Distribution / packaging
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
share/python-wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST

# Virtual environments
venv/
env/
ENV/
.venv/
.env/
.ENV/
env.bak/
venv.bak/

# PyInstaller
*.manifest
*.spec

# Installer logs
pip-log.txt
pip-delete-this-directory.txt

# Unit test / coverage reports
htmlcov/
.tox/
.nox/
.coverage
.coverage.*
.cache
nosetests.xml
coverage.xml
*.cover
*.py,cover
.hypothesis/
.pytest_cache/
cover/

# Jupyter Notebook
.ipynb_checkpoints

# IPython
profile_default/
ipython_config.py

# pyenv
.python-version

# pipenv
Pipfile.lock

# poetry
poetry.lock

# celery beat schedule file
celerybeat-schedule
celerybeat.pid

# SageMath parsed files
*.sage.py

# Spyder project settings
.spyderproject
.spyproject

# Rope project settings
.ropeproject

# mkdocs documentation
/site

# mypy
.mypy_cache/
.dmypy.json
dmypy.json

# Pyre type checker
.pyre/

# pytype static type analyzer
.pytype/

# Cython debug symbols
cython_debug/

# ===================================
# REPORTS & OUTPUT FILES
# ===================================

# Generated reports and Excel files
reports/
*.xlsx
*.csv

# Output directories
output/
temp/
tmp/
test_output/

# Log files
*.log
logs/
seo_analysis.log

# Debug files
debug_*.py
test_*.py
!tests/

# ===================================
# DEVELOPMENT & IDE
# ===================================

# VS Code
.vscode/
*.code-workspace

# PyCharm
.idea/
*.iml
*.ipr
*.iws

# Sublime Text
*.sublime-project
*.sublime-workspace

# Vim
*.swp
*.swo
*~

# Emacs
*~
\#*\#
/.emacs.desktop
/.emacs.desktop.lock
*.elc
auto-save-list
tramp
.\#*

# ===================================
# OPERATING SYSTEM
# ===================================

# macOS
.DS_Store
.DS_Store?
.AppleDouble
.LSOverride
Icon
._*
.DocumentRevisions-V100
.fseventsd
.Spotlight-V100
.TemporaryItems
.Trashes
.VolumeIcon.icns
.com.apple.timemachine.donotpresent
.AppleDB
.AppleDesktop
Network Trash Folder
Temporary Items
.apdisk

# Windows
Thumbs.db
Thumbs.db:encryptable
ehthumbs.db
ehthumbs_vista.db
*.stackdump
[Dd]esktop.ini
$RECYCLE.BIN/
*.cab
*.msi
*.msix
*.msm
*.msp
*.lnk

# Linux
*~
.fuse_hidden*
.directory
.Trash-*
.nfs*

# ===================================
# WEB & FRONTEND
# ===================================

# Node.js
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*
lerna-debug.log*
.pnpm-debug.log*

# Runtime data
pids
*.pid
*.seed
*.pid.lock

# Coverage directory used by tools like istanbul
coverage/
*.lcov

# nyc test coverage
.nyc_output

# Grunt intermediate storage
.grunt

# Bower dependency directory
bower_components

# node-waf configuration
.lock-wscript

# Compiled binary addons
build/Release

# Dependency directories
jspm_packages/

# TypeScript cache
*.tsbuildinfo

# Optional npm cache directory
.npm

# Optional eslint cache
.eslintcache

# Optional stylelint cache
.stylelintcache

# Microbundle cache
.rpt2_cache/
.rts2_cache_cjs/
.rts2_cache_es/
.rts2_cache_umd/

# Optional REPL history
.node_repl_history

# Output of 'npm pack'
*.tgz

# Yarn Integrity file
.yarn-integrity

# parcel-bundler cache
.cache
.parcel-cache

# Next.js build output
.next
out

# Nuxt.js build / generate output
.nuxt
dist

# Gatsby files
.cache/
public

# Storybook build outputs
.out
.storybook-out
storybook-static

# ===================================
# DATABASE & MIGRATION
# ===================================

# Database files
*.db
*.sqlite
*.sqlite3

# Migration backup files
*_backup.sql
migration_*.sql
!database_migration.sql

# ===================================
# DOCKER & DEPLOYMENT
# ===================================

# Docker
.dockerignore
docker-compose.override.yml

# ===================================
# PROJECT SPECIFIC
# ===================================

# Backup files
backup_*/
*_backup/
*.backup
*.bak*

# Playwright browsers
.playwright/

# Chrome/Selenium drivers
chromedriver*
geckodriver*

# Crawl data and URLs
urls_to_crawl.txt
failed_urls.txt
crawl_data/

# Analysis task files
task_*.json
analysis_*.json

# Temporary Excel files
~$*.xlsx
~$*.xls

# Migration and test scripts (keep examples)
migrate_*.py
!migrate_database.py
debug_*.py
test_*.py
!tests/
verify_*.py
!verify_migration.py

# Configuration files (keep examples)
*config.json
!config_example_no_supabase.json

# MCP server files
mcp_server/

# Archive directory
archive/


