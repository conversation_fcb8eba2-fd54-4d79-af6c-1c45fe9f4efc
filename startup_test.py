#!/usr/bin/env python3
"""
Startup test script to verify environment before main application starts
"""
import os
import sys

def test_environment():
    """Test the environment and dependencies"""
    print("🔍 Testing startup environment...")
    
    # Test 1: Python version
    print(f"✅ Python version: {sys.version}")
    
    # Test 2: Working directory
    cwd = os.getcwd()
    print(f"✅ Working directory: {cwd}")
    
    # Test 3: Required files
    required_files = ['main.py', 'requirements.txt', 'entrypoint.sh']
    for file in required_files:
        if os.path.exists(file):
            print(f"✅ Found: {file}")
        else:
            print(f"❌ Missing: {file}")
            return False
    
    # Test 4: Environment variables
    port = os.getenv('PORT', 'not set')
    supabase_url = os.getenv('SUPABASE_URL', 'not set')
    supabase_key = os.getenv('SUPABASE_KEY', 'not set')
    
    print(f"✅ PORT: {port}")
    print(f"✅ SUPABASE_URL: {supabase_url}")
    print(f"✅ SUPABASE_KEY: {'set' if supabase_key != 'not set' else 'not set'}")
    
    # Test 5: Python path
    sys.path.insert(0, os.path.join(cwd, 'src'))
    print(f"✅ Python path updated: {sys.path[:3]}...")
    
    # Test 6: Critical imports
    try:
        import uvicorn
        print("✅ uvicorn imported")
        
        import fastapi
        print("✅ fastapi imported")
        
        from src.config.settings import settings
        print("✅ settings imported")

        # Show the actual port that will be used (same logic as main.py)
        actual_port = int(os.getenv('PORT', settings.port or settings.api_port))

        print(f"✅ Settings API host: {settings.api_host}")
        print(f"✅ Settings API port: {settings.api_port}")
        print(f"✅ Actual port (from PORT env): {actual_port}")

    except Exception as e:
        print(f"❌ Import error: {e}")
        import traceback
        traceback.print_exc()
        return False

    # Test 7: Network connectivity to Supabase
    try:
        import socket
        import urllib.parse

        supabase_url = os.getenv('SUPABASE_URL', '')
        if supabase_url:
            parsed = urllib.parse.urlparse(supabase_url)
            hostname = parsed.hostname
            port = parsed.port or (443 if parsed.scheme == 'https' else 80)

            print(f"🌐 Testing network connectivity to {hostname}:{port}...")

            # Test DNS resolution
            try:
                ip = socket.gethostbyname(hostname)
                print(f"✅ DNS resolution: {hostname} -> {ip}")
            except Exception as dns_error:
                print(f"❌ DNS resolution failed: {dns_error}")
                return False

            # Test TCP connection
            try:
                sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
                sock.settimeout(10)
                result = sock.connect_ex((hostname, port))
                sock.close()

                if result == 0:
                    print(f"✅ TCP connection successful to {hostname}:{port}")
                else:
                    print(f"❌ TCP connection failed to {hostname}:{port} (error: {result})")
                    return False
            except Exception as tcp_error:
                print(f"❌ TCP connection error: {tcp_error}")
                return False
        else:
            print("⚠️ SUPABASE_URL not set, skipping network test")

    except Exception as e:
        print(f"❌ Network test error: {e}")
        return False
    
    print("🎯 All startup tests passed!")
    return True

if __name__ == "__main__":
    if test_environment():
        print("✅ Startup test successful")
        sys.exit(0)
    else:
        print("❌ Startup test failed")
        sys.exit(1)
