FROM python:3.11-slim

# Set working directory
WORKDIR /app

# Install system dependencies
RUN apt-get update && apt-get install -y \
    gcc \
    g++ \
    curl \
    && rm -rf /var/lib/apt/lists/*

# Copy requirements first for better caching
COPY requirements.txt .

# Install Python dependencies
RUN pip install --no-cache-dir -r requirements.txt

# Copy application code
COPY . .

# Create necessary directories
RUN mkdir -p reports temp logs public

# Set permissions
RUN chmod 755 reports temp logs public

# Make entrypoint script executable
RUN chmod +x entrypoint.sh

# Expose port (Railway will set PORT environment variable)
EXPOSE 8000

# Add health check for Railway
HEALTHCHECK --interval=30s --timeout=10s --start-period=60s --retries=3 \
    CMD curl -f http://localhost:8000/health || exit 1

# Use entrypoint script to ensure proper environment variable handling
ENTRYPOINT ["./entrypoint.sh"]
