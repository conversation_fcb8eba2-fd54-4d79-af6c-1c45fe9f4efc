#!/bin/bash

echo "🚀 Starting SEO Analysis Tool..."
echo "Current time: $(date)"
echo "Working directory: $(pwd)"
echo "Python version: $(python --version)"

echo "Environment variables:"
echo "  PORT: ${PORT:-'not set'}"
echo "  SUPABASE_URL: ${SUPABASE_URL:-'not set'}"
echo "  SUPABASE_KEY: ${SUPABASE_KEY:0:20}..." # Show first 20 chars only

# Ensure PORT is set to a default if not provided
export PORT=${PORT:-8000}

echo "Using PORT: $PORT"

# Check if main.py exists
if [ ! -f "main.py" ]; then
    echo "❌ ERROR: main.py not found in $(pwd)"
    echo "Files in current directory:"
    ls -la
    exit 1
fi

# Run startup test
echo "🔍 Running startup test..."
python startup_test.py

if [ $? -ne 0 ]; then
    echo "❌ Startup test failed"
    exit 1
fi

# Start the application
echo "🌟 Starting Python application..."
echo "Command: python main.py"
exec python main.py
