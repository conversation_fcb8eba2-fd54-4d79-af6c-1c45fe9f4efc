#!/usr/bin/env python3
"""
Minimal FastAPI app for Railway debugging
"""
import os
import sys
from fastapi import FastAPI

print("🚀 Starting minimal FastAPI app...")
print(f"Python version: {sys.version}")
print(f"Working directory: {os.getcwd()}")
print(f"PORT environment: {os.getenv('PORT', 'Not set')}")
print(f"Files in directory: {os.listdir('.')}")
print("Environment variables:")
for key, value in os.environ.items():
    if 'SECRET' not in key.upper() and 'KEY' not in key.upper():
        print(f"  {key}={value}")
    else:
        print(f"  {key}=***hidden***")

# Create minimal FastAPI app
app = FastAPI(title="SEO Analysis Tool - Minimal", version="1.0.0")

@app.get("/")
async def root():
    return {
        "message": "SEO Analysis Tool is running!",
        "status": "minimal_mode",
        "python_version": sys.version,
        "port": os.getenv('PORT', '8000')
    }

@app.get("/health")
async def health():
    return {"status": "healthy", "mode": "minimal"}

@app.get("/debug")
async def debug():
    return {
        "environment": dict(os.environ),
        "working_directory": os.getcwd(),
        "files": os.listdir('.'),
        "python_path": sys.path
    }

print("✅ Minimal FastAPI app created successfully")

if __name__ == "__main__":
    import uvicorn
    port = int(os.getenv('PORT', 8000))
    print(f"🌟 Starting server on port {port}")
    uvicorn.run(app, host="0.0.0.0", port=port)
